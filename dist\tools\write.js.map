{"version": 3, "file": "write.js", "sourceRoot": "", "sources": ["../../src/tools/write.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAG7B,MAAa,SAAS;IACpB,IAAI,GAAG,OAAO,CAAC;IACf,WAAW,GAAG,sHAAsH,CAAC;IACrI,UAAU,GAAG;QACX,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,2BAA2B;aACzC;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,kCAAkC;aAChD;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,+BAA+B;gBAC5C,OAAO,EAAE,MAAM;aAChB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;gBACnC,WAAW,EAAE,mEAAmE;gBAChF,OAAO,EAAE,OAAO;aACjB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,gDAAgD;gBAC7D,OAAO,EAAE,IAAI;aACd;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,+CAA+C;gBAC5D,OAAO,EAAE,KAAK;aACf;SACF;QACD,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;KAC9B,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,IAOb;QACC,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,EAAE,QAAQ,EACd,OAAO,EACP,QAAQ,GAAG,MAAM,EACjB,IAAI,GAAG,OAAO,EACd,WAAW,GAAG,IAAI,EAClB,MAAM,GAAG,KAAK,EACf,GAAG,IAAI,CAAC;YAET,qBAAqB;YACrB,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,uBAAuB;YACvB,MAAM,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAE3C,yBAAyB;YACzB,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,QAAQ;oBACX,IAAI,UAAU,EAAE,CAAC;wBACf,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,MAAM,EAAE,EAAE;4BACV,KAAK,EAAE,wBAAwB,QAAQ,EAAE;yBAC1C,CAAC;oBACJ,CAAC;oBACD,MAAM;gBAER,KAAK,OAAO;oBACV,IAAI,UAAU,IAAI,MAAM,EAAE,CAAC;wBACzB,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;oBACD,MAAM;gBAER,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC7E,CAAC;YAED,sCAAsC;YACtC,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACxB,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;YAED,iBAAiB;YACjB,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,QAA0B,CAAC,CAAC;YAEhE,iBAAiB;YACjB,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEpC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,8BAA8B,QAAQ,EAAE;gBAChD,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,QAAQ;oBACR,IAAI;oBACJ,OAAO,EAAE,CAAC,UAAU;oBACpB,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;iBAClC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,sBAAsB;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,QAAgB,EAChB,OAAe,EACf,QAAgB,EAChB,WAAoB;QAEpB,IAAI,CAAC;YACH,sCAAsC;YACtC,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACxB,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;YAED,iBAAiB;YACjB,EAAE,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,QAA0B,CAAC,CAAC;YAEjE,iBAAiB;YACjB,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEpC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,6BAA6B,QAAQ,EAAE;gBAC/C,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,QAAQ;oBACR,IAAI,EAAE,QAAQ;oBACd,aAAa,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,QAA0B,CAAC;iBACtE;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,GAAG,QAAQ,WAAW,SAAS,EAAE,CAAC;QAErD,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACtC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,wCAAwC;IACxC,KAAK,CAAC,kBAAkB,CAAC,KAIvB;QACA,MAAM,OAAO,GAAU,EAAE,CAAC;QAC1B,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;oBAChC,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB,CAAC,CAAC;gBAEH,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,KAAK,EAAE,MAAM,CAAC,KAAK;iBACpB,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,YAAY,EAAE,CAAC;gBACjB,CAAC;qBAAM,CAAC;oBACN,UAAU,EAAE,CAAC;gBACf,CAAC;YACH,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC;gBACH,UAAU,EAAE,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,UAAU,KAAK,CAAC;YACzB,MAAM,EAAE,SAAS,YAAY,wBAAwB,UAAU,SAAS;YACxE,QAAQ,EAAE;gBACR,UAAU,EAAE,KAAK,CAAC,MAAM;gBACxB,YAAY;gBACZ,UAAU;gBACV,OAAO;aACR;SACF,CAAC;IACJ,CAAC;IAED,uCAAuC;IACvC,KAAK,CAAC,iBAAiB,CACrB,YAAoB,EACpB,UAAkB,EAClB,SAAiC;QAEjC,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,4BAA4B,YAAY,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,QAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAErD,gCAAgC;YAChC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,SAAS,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAC;gBACpD,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAC5C,CAAC;YAED,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;gBACxB,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,QAAQ;aAClB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,kCAAkC,KAAK,CAAC,OAAO,EAAE;aACzD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,8BAA8B;IAC9B,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,IAAS,EAAE,SAAkB,IAAI;QACjE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAE9E,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;gBACxB,IAAI,EAAE,QAAQ;gBACd,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,yBAAyB,KAAK,CAAC,OAAO,EAAE;aAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,6BAA6B;IAC7B,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,IAAa,EAAE,OAAkB;QAChE,IAAI,CAAC;YACH,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YACtC,CAAC;YAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YACvF,CAAC;YAED,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;gBACxB,IAAI,EAAE,QAAQ;gBACd,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,EAAE;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AArSD,8BAqSC"}