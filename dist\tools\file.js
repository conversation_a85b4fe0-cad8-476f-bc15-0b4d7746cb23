"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileTool = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const util_1 = require("util");
const readFile = (0, util_1.promisify)(fs.readFile);
const writeFile = (0, util_1.promisify)(fs.writeFile);
const mkdir = (0, util_1.promisify)(fs.mkdir);
const stat = (0, util_1.promisify)(fs.stat);
const readdir = (0, util_1.promisify)(fs.readdir);
class FileTool {
    name = 'file';
    description = 'Read, write, and manage files and directories. Can read file contents, create directories, check file stats, and list directory contents.';
    parameters = {
        type: 'object',
        properties: {
            action: {
                type: 'string',
                enum: ['read', 'write', 'append', 'delete', 'exists', 'stat', 'mkdir', 'list', 'copy', 'move'],
                description: 'The file operation to perform'
            },
            path: {
                type: 'string',
                description: 'The file or directory path'
            },
            content: {
                type: 'string',
                description: 'Content to write (for write/append actions)'
            },
            encoding: {
                type: 'string',
                description: 'File encoding (default: utf8)',
                default: 'utf8'
            },
            recursive: {
                type: 'boolean',
                description: 'Create parent directories if they don\'t exist (for mkdir)',
                default: false
            },
            destination: {
                type: 'string',
                description: 'Destination path (for copy/move actions)'
            }
        },
        required: ['action', 'path']
    };
    async execute(args) {
        try {
            const { action, path: filePath, content, encoding = 'utf8', recursive = false, destination } = args;
            switch (action) {
                case 'read':
                    return await this.readFile(filePath, encoding);
                case 'write':
                    return await this.writeFile(filePath, content || '', encoding);
                case 'append':
                    return await this.appendFile(filePath, content || '', encoding);
                case 'delete':
                    return await this.deleteFile(filePath);
                case 'exists':
                    return await this.fileExists(filePath);
                case 'stat':
                    return await this.getFileStat(filePath);
                case 'mkdir':
                    return await this.createDirectory(filePath, recursive);
                case 'list':
                    return await this.listDirectory(filePath);
                case 'copy':
                    if (!destination) {
                        throw new Error('Destination path required for copy action');
                    }
                    return await this.copyFile(filePath, destination);
                case 'move':
                    if (!destination) {
                        throw new Error('Destination path required for move action');
                    }
                    return await this.moveFile(filePath, destination);
                default:
                    throw new Error(`Unknown action: ${action}`);
            }
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error.message || 'File operation failed'
            };
        }
    }
    async readFile(filePath, encoding) {
        try {
            const content = await readFile(filePath, encoding);
            return {
                success: true,
                output: content,
                metadata: {
                    path: filePath,
                    size: content.length,
                    encoding
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to read file: ${error.message}`
            };
        }
    }
    async writeFile(filePath, content, encoding) {
        try {
            // Ensure directory exists
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                await mkdir(dir, { recursive: true });
            }
            await writeFile(filePath, content, encoding);
            return {
                success: true,
                output: `File written successfully: ${filePath}`,
                metadata: {
                    path: filePath,
                    size: content.length,
                    encoding
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to write file: ${error.message}`
            };
        }
    }
    async appendFile(filePath, content, encoding) {
        try {
            await fs.promises.appendFile(filePath, content, encoding);
            return {
                success: true,
                output: `Content appended to file: ${filePath}`,
                metadata: {
                    path: filePath,
                    appendedSize: content.length,
                    encoding
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to append to file: ${error.message}`
            };
        }
    }
    async deleteFile(filePath) {
        try {
            const stats = await stat(filePath);
            if (stats.isDirectory()) {
                await fs.promises.rmdir(filePath, { recursive: true });
                return {
                    success: true,
                    output: `Directory deleted: ${filePath}`,
                    metadata: { path: filePath, type: 'directory' }
                };
            }
            else {
                await fs.promises.unlink(filePath);
                return {
                    success: true,
                    output: `File deleted: ${filePath}`,
                    metadata: { path: filePath, type: 'file' }
                };
            }
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to delete: ${error.message}`
            };
        }
    }
    async fileExists(filePath) {
        try {
            await stat(filePath);
            return {
                success: true,
                output: `File exists: ${filePath}`,
                metadata: { path: filePath, exists: true }
            };
        }
        catch (error) {
            return {
                success: true,
                output: `File does not exist: ${filePath}`,
                metadata: { path: filePath, exists: false }
            };
        }
    }
    async getFileStat(filePath) {
        try {
            const stats = await stat(filePath);
            const statInfo = {
                path: filePath,
                size: stats.size,
                isFile: stats.isFile(),
                isDirectory: stats.isDirectory(),
                created: stats.birthtime,
                modified: stats.mtime,
                accessed: stats.atime,
                permissions: stats.mode.toString(8)
            };
            return {
                success: true,
                output: JSON.stringify(statInfo, null, 2),
                metadata: statInfo
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to get file stats: ${error.message}`
            };
        }
    }
    async createDirectory(dirPath, recursive) {
        try {
            await mkdir(dirPath, { recursive });
            return {
                success: true,
                output: `Directory created: ${dirPath}`,
                metadata: { path: dirPath, recursive }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to create directory: ${error.message}`
            };
        }
    }
    async listDirectory(dirPath) {
        try {
            const files = await readdir(dirPath, { withFileTypes: true });
            const fileList = files.map(file => ({
                name: file.name,
                type: file.isDirectory() ? 'directory' : 'file',
                path: path.join(dirPath, file.name)
            }));
            return {
                success: true,
                output: JSON.stringify(fileList, null, 2),
                metadata: {
                    path: dirPath,
                    count: files.length,
                    files: fileList
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to list directory: ${error.message}`
            };
        }
    }
    async copyFile(sourcePath, destPath) {
        try {
            await fs.promises.copyFile(sourcePath, destPath);
            return {
                success: true,
                output: `File copied from ${sourcePath} to ${destPath}`,
                metadata: { source: sourcePath, destination: destPath }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to copy file: ${error.message}`
            };
        }
    }
    async moveFile(sourcePath, destPath) {
        try {
            await fs.promises.rename(sourcePath, destPath);
            return {
                success: true,
                output: `File moved from ${sourcePath} to ${destPath}`,
                metadata: { source: sourcePath, destination: destPath }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to move file: ${error.message}`
            };
        }
    }
}
exports.FileTool = FileTool;
//# sourceMappingURL=file.js.map