{"version": 3, "file": "manager.js", "sourceRoot": "", "sources": ["../../src/context/manager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,+BAA4B;AAG5B,MAAa,cAAc;IACjB,KAAK,GAA6B,IAAI,GAAG,EAAE,CAAC;IAC5C,SAAS,CAAS;IACT,eAAe,GAAG,IAAI,CAAC,CAAC,mCAAmC;IAE5E,YAAY,YAAoB,OAAO;QACrC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,QAAgB;QAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE5C,IAAI,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5C,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,OAAe;QACxC,MAAM,QAAQ,GAAG;YACf,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;YAC5C,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ;YAC5C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU;YAC3C,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY;YAChD,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU;YAChD,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS;YAC5C,iBAAiB,EAAE,eAAe,EAAE,UAAU;SAC/C,CAAC;QAEF,MAAM,cAAc,GAAG;YACrB,oBAAoB;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,UAAU;YACV,UAAU;YACV,WAAW;SACZ,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,MAAM,IAAA,WAAI,EAAC,OAAO,EAAE;gBAChC,GAAG,EAAE,OAAO;gBACZ,MAAM,EAAE,cAAc;gBACtB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YAEH,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,sBAAsB,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAAgB;QAC1C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAEnD,yCAAyC;YACzC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,qBAAqB;gBAC7E,OAAO;YACT,CAAC;YAED,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,QAAQ;gBACd,OAAO;gBACP,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,YAAY,EAAE,KAAK,CAAC,KAAK;gBACzB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;aACxC,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED,UAAU,CAAC,QAAgB;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;IAED,UAAU;QACR,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED,cAAc;QACZ,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ;QACN,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAE5C,IAAI,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAED,gFAAgF;QAChF,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YACzE,wBAAwB;YACxB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;gBACxB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;YAED,qCAAqC;YACrC,MAAM,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YACrE,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,QAAQ;gBAC3C,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC;YAED,2BAA2B;YAC3B,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,iDAAiD;QACjD,IAAI,cAAc,GAAG,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;QACpD,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YACrD,IAAI,cAAc,IAAI,CAAC;gBAAE,MAAM;YAE/B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;YACzE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5B,cAAc,IAAI,UAAU,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QAC3C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,qCAAqC,UAAU,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAEO,YAAY,CAAC,OAAe;QAClC,qDAAqD;QACrD,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAEO,cAAc,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,WAAW,GAA2B;YAC1C,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,GAAG;YACT,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,MAAM;YACb,QAAQ,EAAE,OAAO;YACjB,KAAK,EAAE,QAAQ;YACf,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,UAAU;YACjB,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,MAAM;YACb,aAAa,EAAE,YAAY;SAC5B,CAAC;QAEF,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC;IACpC,CAAC;IAEO,UAAU,CAAC,QAAgB;QACjC,MAAM,cAAc,GAAG;YACrB,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;YAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;YACzC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ;SACjC,CAAC;QAEF,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,oCAAoC;IACpC,iBAAiB;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAChC,MAAM,OAAO,GAAG;YACd,UAAU,EAAE,KAAK,CAAC,MAAM;YACxB,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,SAAS,EAAE,EAA4B;YACvC,WAAW,EAAE,KAAK;iBACf,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;iBACnE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;iBACZ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;SACtD,CAAC;QAEF,0BAA0B;QAC1B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC;YACxC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED,gCAAgC;IAChC,iBAAiB,CAAC,OAAe;QAC/B,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACrC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAClD,CAAC;IACJ,CAAC;IAED,wBAAwB;IACxB,kBAAkB,CAAC,QAAgB;QACjC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACtE,CAAC;CACF;AApOD,wCAoOC"}