import { EventEmitter } from 'events';
import { CLIConfig } from '../types';
export declare class CLIInterface extends EventEmitter {
    private rl;
    private contextManager;
    private modelManager;
    private toolManager;
    private planExecutor;
    private errorDetector;
    private diffReviewer;
    private currentSession;
    private config;
    private spinner;
    constructor(config: CLIConfig);
    private setupEventHandlers;
    private initializeSession;
    start(): void;
    private displayWelcome;
    private displayStatus;
    private handleUserInput;
    private handleCommand;
    private displayHelp;
    private processAIQuery;
    private handleToolCalls;
    private generateSessionId;
    private handleExit;
    private displayModels;
    private switchModel;
    private handleContextCommand;
    private addToContext;
    private removeFromContext;
    private listContext;
    private scanForErrors;
    private autoFixErrors;
    private clearSession;
    private handleStepStart;
    private handleStepComplete;
    private handlePlanComplete;
    private handleErrorDetected;
}
//# sourceMappingURL=interface.d.ts.map