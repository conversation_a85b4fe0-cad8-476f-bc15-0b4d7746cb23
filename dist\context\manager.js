"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const glob_1 = require("glob");
class ContextManager {
    files = new Map();
    maxTokens;
    TOKENS_PER_CHAR = 0.25; // Approximate tokens per character
    constructor(maxTokens = 2000000) {
        this.maxTokens = maxTokens;
    }
    async addFile(filePath) {
        const resolvedPath = path.resolve(filePath);
        if (fs.statSync(resolvedPath).isDirectory()) {
            await this.addDirectory(resolvedPath);
        }
        else {
            await this.addSingleFile(resolvedPath);
        }
        this.optimize();
    }
    async addDirectory(dirPath) {
        const patterns = [
            '**/*.ts', '**/*.js', '**/*.tsx', '**/*.jsx',
            '**/*.py', '**/*.java', '**/*.cpp', '**/*.c',
            '**/*.cs', '**/*.go', '**/*.rs', '**/*.php',
            '**/*.rb', '**/*.swift', '**/*.kt', '**/*.scala',
            '**/*.json', '**/*.yaml', '**/*.yml', '**/*.xml',
            '**/*.md', '**/*.txt', '**/*.sql', '**/*.sh',
            '**/*.dockerfile', '**/Dockerfile', '**/*.env'
        ];
        const ignorePatterns = [
            '**/node_modules/**',
            '**/dist/**',
            '**/build/**',
            '**/.git/**',
            '**/coverage/**',
            '**/*.log',
            '**/*.tmp',
            '**/*.temp'
        ];
        for (const pattern of patterns) {
            const files = await (0, glob_1.glob)(pattern, {
                cwd: dirPath,
                ignore: ignorePatterns,
                absolute: true
            });
            for (const file of files) {
                try {
                    await this.addSingleFile(file);
                }
                catch (error) {
                    console.warn(`Failed to add file ${file}: ${error}`);
                }
            }
        }
    }
    async addSingleFile(filePath) {
        try {
            const stats = fs.statSync(filePath);
            const content = fs.readFileSync(filePath, 'utf-8');
            // Skip binary files and very large files
            if (this.isBinaryFile(content) || stats.size > 1000000) { // 1MB limit per file
                return;
            }
            const contextFile = {
                path: filePath,
                content,
                size: stats.size,
                lastModified: stats.mtime,
                language: this.detectLanguage(filePath)
            };
            this.files.set(filePath, contextFile);
        }
        catch (error) {
            throw new Error(`Failed to read file ${filePath}: ${error}`);
        }
    }
    removeFile(filePath) {
        const resolvedPath = path.resolve(filePath);
        this.files.delete(resolvedPath);
    }
    getContext() {
        return Array.from(this.files.values());
    }
    getTotalTokens() {
        let totalChars = 0;
        for (const file of this.files.values()) {
            totalChars += file.content.length;
        }
        return Math.ceil(totalChars * this.TOKENS_PER_CHAR);
    }
    optimize() {
        const currentTokens = this.getTotalTokens();
        if (currentTokens <= this.maxTokens) {
            return;
        }
        // Sort files by importance (smaller files, recently modified, code files first)
        const sortedFiles = Array.from(this.files.entries()).sort(([, a], [, b]) => {
            // Prioritize code files
            const aIsCode = this.isCodeFile(a.path);
            const bIsCode = this.isCodeFile(b.path);
            if (aIsCode !== bIsCode) {
                return bIsCode ? 1 : -1;
            }
            // Prioritize recently modified files
            const timeDiff = b.lastModified.getTime() - a.lastModified.getTime();
            if (Math.abs(timeDiff) > 86400000) { // 1 day
                return timeDiff > 0 ? 1 : -1;
            }
            // Prioritize smaller files
            return a.size - b.size;
        });
        // Remove files until we're under the token limit
        let tokensToRemove = currentTokens - this.maxTokens;
        for (const [filePath, file] of sortedFiles.reverse()) {
            if (tokensToRemove <= 0)
                break;
            const fileTokens = Math.ceil(file.content.length * this.TOKENS_PER_CHAR);
            this.files.delete(filePath);
            tokensToRemove -= fileTokens;
        }
    }
    async loadWorkingDirectory(workingDir) {
        if (!fs.existsSync(workingDir)) {
            throw new Error(`Working directory does not exist: ${workingDir}`);
        }
        await this.addDirectory(workingDir);
    }
    isBinaryFile(content) {
        // Check for null bytes which indicate binary content
        return content.includes('\0');
    }
    detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.xml': 'xml',
            '.md': 'markdown',
            '.sql': 'sql',
            '.sh': 'bash',
            '.dockerfile': 'dockerfile'
        };
        return languageMap[ext] || 'text';
    }
    isCodeFile(filePath) {
        const codeExtensions = [
            '.ts', '.tsx', '.js', '.jsx', '.py', '.java',
            '.cpp', '.c', '.cs', '.go', '.rs', '.php',
            '.rb', '.swift', '.kt', '.scala'
        ];
        const ext = path.extname(filePath).toLowerCase();
        return codeExtensions.includes(ext);
    }
    // Get context summary for AI models
    getContextSummary() {
        const files = this.getContext();
        const summary = {
            totalFiles: files.length,
            totalTokens: this.getTotalTokens(),
            languages: {},
            recentFiles: files
                .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
                .slice(0, 10)
                .map(f => ({ path: f.path, language: f.language }))
        };
        // Count files by language
        for (const file of files) {
            const lang = file.language || 'unknown';
            summary.languages[lang] = (summary.languages[lang] || 0) + 1;
        }
        return JSON.stringify(summary, null, 2);
    }
    // Get specific files by pattern
    getFilesByPattern(pattern) {
        const regex = new RegExp(pattern, 'i');
        return this.getContext().filter(file => regex.test(file.path) || regex.test(file.content));
    }
    // Get files by language
    getFilesByLanguage(language) {
        return this.getContext().filter(file => file.language === language);
    }
}
exports.ContextManager = ContextManager;
//# sourceMappingURL=manager.js.map