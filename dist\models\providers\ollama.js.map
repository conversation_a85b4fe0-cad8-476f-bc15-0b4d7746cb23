{"version": 3, "file": "ollama.js", "sourceRoot": "", "sources": ["../../../src/models/providers/ollama.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAG1B,MAAa,cAAc;IACjB,OAAO,CAAS;IAExB,YAAY,UAAkB,wBAAwB;QACpD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,QAAmB,EACnB,KAAa,EACb,MAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAE7C,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBAC5D,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBACvD,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM,CAAC,WAAW;oBAC/B,KAAK,EAAE,MAAM,CAAC,IAAI;oBAClB,WAAW,EAAE,MAAM,CAAC,SAAS;iBAC9B;gBACD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE3B,8BAA8B;YAC9B,IAAI,SAAS,GAAU,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;gBAC7B,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,CAAC;oBACpD,EAAE,EAAE,EAAE,CAAC,QAAQ,EAAE,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACnD,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,EAAE,CAAC,QAAQ,EAAE,IAAI;wBACvB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,IAAI,EAAE,CAAC;qBACxD;iBACF,CAAC,CAAC,CAAC;YACN,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE;gBACpC,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;aACxD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,QAAmB,EACnB,KAAa,EACb,MAAmB,EACnB,OAAgC;QAEhC,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAE7C,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBAC5D,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBACvD,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM,CAAC,WAAW;oBAC/B,KAAK,EAAE,MAAM,CAAC,IAAI;oBAClB,WAAW,EAAE,MAAM,CAAC,SAAS;iBAC9B;gBACD,MAAM,EAAE,IAAI;aACb,EAAE;gBACD,YAAY,EAAE,QAAQ;aACvB,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE;gBACzC,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAEvE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,CAAC;wBACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAC9B,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;4BAC1B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBAChC,CAAC;oBACH,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,8CAA8C;oBAChD,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAmB;QACzC,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACxB,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;gBACjB,KAAK,QAAQ;oBACX,OAAO;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC;gBACJ,KAAK,MAAM;oBACT,OAAO;wBACL,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC;gBACJ,KAAK,WAAW;oBACd,MAAM,YAAY,GAAQ;wBACxB,IAAI,EAAE,WAAW;wBACjB,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC;oBAEF,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;wBACnB,YAAY,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;4BAClD,QAAQ,EAAE;gCACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;gCACtB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;6BAC7C;yBACF,CAAC,CAAC,CAAC;oBACN,CAAC;oBAED,OAAO,YAAY,CAAC;gBACtB,KAAK,UAAU;oBACb,OAAO;wBACL,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC;gBACJ;oBACE,MAAM,IAAI,KAAK,CAAC,6BAA6B,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,KAAa;QAChC,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,CAAC,CAAC;YAC7D,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC,WAAW;QACxD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,SAAiB;QAC/B,IAAI,CAAC;YACH,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE;gBAC3C,IAAI,EAAE,SAAS;aAChB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAiB;QACjC,IAAI,CAAC;YACH,MAAM,eAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,aAAa,EAAE;gBAC/C,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;aAC1B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;CACF;AA7LD,wCA6LC"}