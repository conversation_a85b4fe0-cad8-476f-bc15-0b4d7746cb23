#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv = __importStar(require("dotenv"));
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const interface_1 = require("./cli/interface");
// Load environment variables
dotenv.config();
const program = new commander_1.Command();
program
    .name('kritrima')
    .description('Kritrima AI - Advanced Local CLI Terminal LLM Interaction Environment')
    .version('1.0.0');
program
    .option('-m, --model <model>', 'AI model to use', 'gpt-4-turbo-preview')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-c, --context-size <size>', 'Context size in tokens', '2000000')
    .option('-a, --autonomy <level>', 'Autonomy level (full|guided|manual)', 'guided')
    .option('--auto-fix', 'Enable automatic error fixing', false)
    .option('--log-level <level>', 'Log level (debug|info|warn|error)', 'info')
    .option('--config <path>', 'Configuration file path')
    .action(async (options) => {
    try {
        // Load configuration
        const config = await loadConfig(options);
        // Validate environment
        await validateEnvironment();
        // Create and start CLI interface
        const cli = new interface_1.CLIInterface(config);
        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log(chalk_1.default.blue('\n\nShutting down gracefully...'));
            process.exit(0);
        });
        process.on('SIGTERM', () => {
            console.log(chalk_1.default.blue('\n\nShutting down gracefully...'));
            process.exit(0);
        });
        // Start the CLI
        cli.start();
    }
    catch (error) {
        console.error(chalk_1.default.red(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`));
        process.exit(1);
    }
});
// Add subcommands
program
    .command('init')
    .description('Initialize Kritrima AI in current directory')
    .option('-f, --force', 'Force initialization even if already initialized')
    .action(async (options) => {
    await initializeProject(options);
});
program
    .command('scan')
    .description('Scan directory for errors and issues')
    .option('-d, --directory <path>', 'Directory to scan', process.cwd())
    .option('--fix', 'Automatically fix issues where possible')
    .action(async (options) => {
    await scanDirectory(options);
});
program
    .command('models')
    .description('List available AI models')
    .action(async () => {
    await listModels();
});
program
    .command('config')
    .description('Show current configuration')
    .action(async () => {
    await showConfig();
});
async function loadConfig(options) {
    let config = {
        defaultModel: options.model,
        autoFix: options.autoFix,
        contextSize: parseInt(options.contextSize),
        autonomyLevel: options.autonomy,
        logLevel: options.logLevel,
        workingDirectory: options.directory
    };
    // Load from config file if specified
    if (options.config) {
        try {
            const fs = require('fs');
            const configFile = JSON.parse(fs.readFileSync(options.config, 'utf8'));
            config = { ...config, ...configFile };
        }
        catch (error) {
            console.warn(chalk_1.default.yellow(`Warning: Could not load config file: ${error}`));
        }
    }
    return config;
}
async function validateEnvironment() {
    const requiredEnvVars = [];
    const warnings = [];
    // Check for API keys
    if (!process.env.OPENAI_API_KEY) {
        warnings.push('OPENAI_API_KEY not set - OpenAI models will not be available');
    }
    if (!process.env.ANTHROPIC_API_KEY) {
        warnings.push('ANTHROPIC_API_KEY not set - Anthropic models will not be available');
    }
    if (!process.env.DEEPSEEK_API_KEY) {
        warnings.push('DEEPSEEK_API_KEY not set - Deepseek models will not be available');
    }
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 18) {
        throw new Error(`Node.js 18 or higher is required. Current version: ${nodeVersion}`);
    }
    // Show warnings
    if (warnings.length > 0) {
        console.log(chalk_1.default.yellow('\nWarnings:'));
        warnings.forEach(warning => console.log(chalk_1.default.yellow(`  • ${warning}`)));
        console.log();
    }
    // Check if at least one provider is available
    if (!process.env.OPENAI_API_KEY &&
        !process.env.ANTHROPIC_API_KEY &&
        !process.env.DEEPSEEK_API_KEY) {
        // Check if Ollama is running
        try {
            const axios = require('axios');
            await axios.get('http://localhost:11434/api/tags', { timeout: 5000 });
            console.log(chalk_1.default.green('✓ Ollama detected - local models available'));
        }
        catch (error) {
            throw new Error('No AI providers available. Please set API keys or start Ollama.');
        }
    }
}
async function initializeProject(options) {
    const fs = require('fs');
    const path = require('path');
    const configPath = path.join(process.cwd(), '.kritrima.json');
    if (fs.existsSync(configPath) && !options.force) {
        console.log(chalk_1.default.yellow('Kritrima AI already initialized. Use --force to reinitialize.'));
        return;
    }
    const defaultConfig = {
        defaultModel: 'gpt-4-turbo-preview',
        autoFix: false,
        contextSize: 2000000,
        autonomyLevel: 'guided',
        logLevel: 'info',
        workingDirectory: process.cwd(),
        excludePatterns: [
            'node_modules/**',
            'dist/**',
            'build/**',
            '.git/**',
            'coverage/**'
        ]
    };
    fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2));
    // Create .env template
    const envPath = path.join(process.cwd(), '.env.example');
    const envTemplate = `# Kritrima AI Configuration
# Copy this file to .env and fill in your API keys

# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Deepseek API Key
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Ollama Base URL (for local models)
OLLAMA_BASE_URL=http://localhost:11434
`;
    fs.writeFileSync(envPath, envTemplate);
    console.log(chalk_1.default.green('✓ Kritrima AI initialized successfully!'));
    console.log(chalk_1.default.blue('  • Configuration saved to .kritrima.json'));
    console.log(chalk_1.default.blue('  • Environment template saved to .env.example'));
    console.log(chalk_1.default.yellow('\nNext steps:'));
    console.log(chalk_1.default.yellow('  1. Copy .env.example to .env'));
    console.log(chalk_1.default.yellow('  2. Add your API keys to .env'));
    console.log(chalk_1.default.yellow('  3. Run "kritrima" to start'));
}
async function scanDirectory(options) {
    const { ErrorDetector } = require('./core/error-detector');
    console.log(chalk_1.default.blue(`Scanning ${options.directory}...`));
    const detector = new ErrorDetector(options.directory);
    const errors = await detector.scanDirectory(options.directory);
    if (errors.length === 0) {
        console.log(chalk_1.default.green('✓ No issues found'));
        return;
    }
    console.log(chalk_1.default.yellow(`Found ${errors.length} issues:`));
    for (const error of errors) {
        const severity = error.severity === 'error' ? chalk_1.default.red('ERROR') :
            error.severity === 'warning' ? chalk_1.default.yellow('WARN') :
                chalk_1.default.blue('INFO');
        console.log(`  ${severity} ${error.file}:${error.line} - ${error.message}`);
    }
    if (options.fix) {
        const fixableErrors = errors.filter((e) => e.fixable);
        console.log(chalk_1.default.blue(`\nAttempting to fix ${fixableErrors.length} issues...`));
        for (const error of fixableErrors) {
            try {
                await detector.fixError(error);
                console.log(chalk_1.default.green(`✓ Fixed: ${error.message}`));
            }
            catch (fixError) {
                console.log(chalk_1.default.red(`✗ Failed to fix: ${error.message}`));
            }
        }
    }
}
async function listModels() {
    const { ModelManager } = require('./models/manager');
    const manager = new ModelManager();
    const models = await manager.getAvailableModels();
    console.log(chalk_1.default.bold('\nAvailable Models:'));
    for (const model of models) {
        const status = model.provider === 'ollama' ? '(local)' : '(cloud)';
        console.log(`  ${chalk_1.default.cyan(model.name)} - ${model.provider} ${status}`);
        console.log(`    Max tokens: ${model.maxTokens.toLocaleString()}`);
        console.log(`    Streaming: ${model.supportsStreaming ? '✓' : '✗'}`);
        console.log(`    Function calling: ${model.supportsFunctionCalling ? '✓' : '✗'}`);
        console.log();
    }
}
async function showConfig() {
    const fs = require('fs');
    const path = require('path');
    const configPath = path.join(process.cwd(), '.kritrima.json');
    if (!fs.existsSync(configPath)) {
        console.log(chalk_1.default.yellow('No configuration file found. Run "kritrima init" to create one.'));
        return;
    }
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    console.log(chalk_1.default.bold('\nCurrent Configuration:'));
    console.log(JSON.stringify(config, null, 2));
}
// Parse command line arguments
program.parse();
//# sourceMappingURL=index.js.map