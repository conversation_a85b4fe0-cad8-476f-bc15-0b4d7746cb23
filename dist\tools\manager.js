"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolManager = void 0;
const shell_1 = require("./shell");
const file_1 = require("./file");
const edit_1 = require("./edit");
const write_1 = require("./write");
const grep_1 = require("./grep");
const web_1 = require("./web");
class ToolManager {
    tools = new Map();
    constructor() {
        this.initializeTools();
    }
    initializeTools() {
        const tools = [
            new shell_1.ShellTool(),
            new file_1.FileTool(),
            new edit_1.EditTool(),
            new write_1.WriteTool(),
            new grep_1.GrepTool(),
            new web_1.WebTool()
        ];
        for (const tool of tools) {
            this.tools.set(tool.name, tool);
        }
    }
    getTool(name) {
        return this.tools.get(name);
    }
    getAvailableTools() {
        return Array.from(this.tools.values());
    }
    async executeTool(name, args) {
        const tool = this.tools.get(name);
        if (!tool) {
            throw new Error(`Tool not found: ${name}`);
        }
        return await tool.execute(args);
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
    }
    unregisterTool(name) {
        this.tools.delete(name);
    }
    listTools() {
        return Array.from(this.tools.keys());
    }
    getToolDescription(name) {
        const tool = this.tools.get(name);
        return tool?.description;
    }
    getToolParameters(name) {
        const tool = this.tools.get(name);
        return tool?.parameters;
    }
}
exports.ToolManager = ToolManager;
//# sourceMappingURL=manager.js.map