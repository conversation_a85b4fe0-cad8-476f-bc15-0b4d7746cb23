import { Message, Tool, ModelConfig, StreamingResponse } from '../../types';
export declare class OllamaProvider {
    private baseUrl;
    constructor(baseUrl?: string);
    generateResponse(messages: Message[], tools: Tool[], config: ModelConfig): Promise<StreamingResponse>;
    streamResponse(messages: Message[], tools: Tool[], config: ModelConfig, onChunk: (chunk: string) => void): Promise<void>;
    private convertMessages;
    private convertTools;
    testConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    pullModel(modelName: string): Promise<void>;
    deleteModel(modelName: string): Promise<void>;
}
//# sourceMappingURL=ollama.d.ts.map