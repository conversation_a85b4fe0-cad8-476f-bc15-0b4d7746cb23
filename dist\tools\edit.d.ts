import { Tool, ToolResult } from '../types';
export declare class EditTool implements Tool {
    name: string;
    description: string;
    parameters: {
        type: "object";
        properties: {
            action: {
                type: string;
                enum: string[];
                description: string;
            };
            path: {
                type: string;
                description: string;
            };
            line_start: {
                type: string;
                description: string;
            };
            line_end: {
                type: string;
                description: string;
            };
            content: {
                type: string;
                description: string;
            };
            find: {
                type: string;
                description: string;
            };
            replace: {
                type: string;
                description: string;
            };
            global: {
                type: string;
                description: string;
                default: boolean;
            };
            backup: {
                type: string;
                description: string;
                default: boolean;
            };
        };
        required: string[];
    };
    execute(args: {
        action: string;
        path: string;
        line_start?: number;
        line_end?: number;
        content?: string;
        find?: string;
        replace?: string;
        global?: boolean;
        backup?: boolean;
    }): Promise<ToolResult>;
    private replaceLines;
    private insertLines;
    private deleteLines;
    private findReplace;
    private applyPatch;
    private createBackup;
    private escapeRegex;
}
//# sourceMappingURL=edit.d.ts.map