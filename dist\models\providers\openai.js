"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIProvider = void 0;
const openai_1 = require("openai");
class OpenAIProvider {
    client;
    constructor(apiKey) {
        this.client = new openai_1.OpenAI({
            apiKey: apiKey
        });
    }
    async generateResponse(messages, tools, config) {
        try {
            const openaiMessages = this.convertMessages(messages);
            const openaiTools = this.convertTools(tools);
            const response = await this.client.chat.completions.create({
                model: config.model,
                messages: openaiMessages,
                tools: openaiTools.length > 0 ? openaiTools : undefined,
                tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
                temperature: config.temperature,
                max_tokens: config.maxTokens,
                top_p: config.topP,
                frequency_penalty: config.frequencyPenalty,
                presence_penalty: config.presencePenalty,
                stream: false
            });
            const choice = response.choices[0];
            if (!choice) {
                throw new Error('No response from OpenAI');
            }
            return {
                content: choice.message.content || '',
                done: true,
                toolCalls: choice.message.tool_calls?.map(tc => ({
                    id: tc.id,
                    type: 'function',
                    function: {
                        name: tc.function.name,
                        arguments: tc.function.arguments
                    }
                }))
            };
        }
        catch (error) {
            throw new Error(`OpenAI API error: ${error}`);
        }
    }
    async streamResponse(messages, tools, config, onChunk) {
        try {
            const openaiMessages = this.convertMessages(messages);
            const openaiTools = this.convertTools(tools);
            const stream = await this.client.chat.completions.create({
                model: config.model,
                messages: openaiMessages,
                tools: openaiTools.length > 0 ? openaiTools : undefined,
                tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
                temperature: config.temperature,
                max_tokens: config.maxTokens,
                top_p: config.topP,
                frequency_penalty: config.frequencyPenalty,
                presence_penalty: config.presencePenalty,
                stream: true
            });
            for await (const chunk of stream) {
                const delta = chunk.choices[0]?.delta;
                if (delta?.content) {
                    onChunk(delta.content);
                }
            }
        }
        catch (error) {
            throw new Error(`OpenAI streaming error: ${error}`);
        }
    }
    convertMessages(messages) {
        return messages.map(msg => {
            switch (msg.role) {
                case 'system':
                    return {
                        role: 'system',
                        content: msg.content
                    };
                case 'user':
                    return {
                        role: 'user',
                        content: msg.content
                    };
                case 'assistant':
                    const assistantMsg = {
                        role: 'assistant',
                        content: msg.content
                    };
                    if (msg.tool_calls) {
                        assistantMsg.tool_calls = msg.tool_calls.map(tc => ({
                            id: tc.id,
                            type: 'function',
                            function: {
                                name: tc.function.name,
                                arguments: tc.function.arguments
                            }
                        }));
                    }
                    return assistantMsg;
                case 'function':
                    return {
                        role: 'tool',
                        tool_call_id: msg.name || 'unknown',
                        content: msg.content
                    };
                default:
                    throw new Error(`Unsupported message role: ${msg.role}`);
            }
        });
    }
    convertTools(tools) {
        return tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters
            }
        }));
    }
    async testConnection() {
        try {
            await this.client.models.list();
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async getAvailableModels() {
        try {
            const response = await this.client.models.list();
            return response.data
                .filter(model => model.id.includes('gpt'))
                .map(model => model.id)
                .sort();
        }
        catch (error) {
            return ['gpt-4-turbo-preview', 'gpt-4', 'gpt-3.5-turbo']; // fallback
        }
    }
}
exports.OpenAIProvider = OpenAIProvider;
//# sourceMappingURL=openai.js.map