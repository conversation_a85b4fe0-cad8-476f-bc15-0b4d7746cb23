import { Tool, ToolResult } from '../types';
export declare class GrepTool implements Tool {
    name: string;
    description: string;
    parameters: {
        type: "object";
        properties: {
            pattern: {
                type: string;
                description: string;
            };
            path: {
                type: string;
                description: string;
                default: string;
            };
            recursive: {
                type: string;
                description: string;
                default: boolean;
            };
            case_sensitive: {
                type: string;
                description: string;
                default: boolean;
            };
            regex: {
                type: string;
                description: string;
                default: boolean;
            };
            include_line_numbers: {
                type: string;
                description: string;
                default: boolean;
            };
            context_lines: {
                type: string;
                description: string;
                default: number;
            };
            file_pattern: {
                type: string;
                description: string;
                default: string;
            };
            exclude_dirs: {
                type: string;
                items: {
                    type: string;
                };
                description: string;
                default: string[];
            };
            max_results: {
                type: string;
                description: string;
                default: number;
            };
        };
        required: string[];
    };
    execute(args: {
        pattern: string;
        path?: string;
        recursive?: boolean;
        case_sensitive?: boolean;
        regex?: boolean;
        include_line_numbers?: boolean;
        context_lines?: number;
        file_pattern?: string;
        exclude_dirs?: string[];
        max_results?: number;
    }): Promise<ToolResult>;
    private getFilesToSearch;
    private isTextFile;
    private searchInFiles;
    private searchInContent;
    private formatResults;
    findFunctions(filePath: string, language?: string): Promise<ToolResult>;
    private detectLanguage;
}
//# sourceMappingURL=grep.d.ts.map