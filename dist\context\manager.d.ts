import { ContextFile, ContextManager as IContextManager } from '../types';
export declare class ContextManager implements IContextManager {
    private files;
    private maxTokens;
    private readonly TOKENS_PER_CHAR;
    constructor(maxTokens?: number);
    addFile(filePath: string): Promise<void>;
    private addDirectory;
    private addSingleFile;
    removeFile(filePath: string): void;
    getContext(): ContextFile[];
    getTotalTokens(): number;
    optimize(): void;
    loadWorkingDirectory(workingDir: string): Promise<void>;
    private isBinaryFile;
    private detectLanguage;
    private isCodeFile;
    getContextSummary(): string;
    getFilesByPattern(pattern: string): ContextFile[];
    getFilesByLanguage(language: string): ContextFile[];
}
//# sourceMappingURL=manager.d.ts.map