import { Tool } from '../types';
export declare class ToolManager {
    private tools;
    constructor();
    private initializeTools;
    getTool(name: string): Tool | undefined;
    getAvailableTools(): Tool[];
    executeTool(name: string, args: any): Promise<any>;
    registerTool(tool: Tool): void;
    unregisterTool(name: string): void;
    listTools(): string[];
    getToolDescription(name: string): string | undefined;
    getToolParameters(name: string): any;
}
//# sourceMappingURL=manager.d.ts.map