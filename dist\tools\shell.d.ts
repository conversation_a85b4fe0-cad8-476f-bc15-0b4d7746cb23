import { Tool, ToolResult } from '../types';
export declare class ShellTool implements Tool {
    name: string;
    description: string;
    parameters: {
        type: "object";
        properties: {
            command: {
                type: string;
                description: string;
            };
            cwd: {
                type: string;
                description: string;
            };
            timeout: {
                type: string;
                description: string;
            };
            env: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
    execute(args: {
        command: string;
        cwd?: string;
        timeout?: number;
        env?: Record<string, string>;
    }): Promise<ToolResult>;
    executeInteractive(args: {
        command: string;
        cwd?: string;
        env?: Record<string, string>;
        onOutput?: (data: string) => void;
        onError?: (data: string) => void;
    }): Promise<ToolResult>;
    private isDangerousCommand;
    getSystemInfo(): Promise<ToolResult>;
    listProcesses(): Promise<ToolResult>;
    getCurrentDirectory(): Promise<ToolResult>;
    listFiles(directory?: string): Promise<ToolResult>;
}
//# sourceMappingURL=shell.d.ts.map