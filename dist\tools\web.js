"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebTool = void 0;
const axios_1 = __importDefault(require("axios"));
class WebTool {
    name = 'web';
    description = 'Fetch content from web URLs, search the web, and interact with web APIs. Can download files, scrape content, and make HTTP requests.';
    parameters = {
        type: 'object',
        properties: {
            action: {
                type: 'string',
                enum: ['fetch', 'download', 'search', 'post', 'put', 'delete'],
                description: 'The web action to perform'
            },
            url: {
                type: 'string',
                description: 'The URL to interact with'
            },
            query: {
                type: 'string',
                description: 'Search query (for search action)'
            },
            headers: {
                type: 'object',
                description: 'HTTP headers to include'
            },
            data: {
                type: 'object',
                description: 'Data to send (for POST/PUT requests)'
            },
            timeout: {
                type: 'number',
                description: 'Request timeout in milliseconds',
                default: 30000
            },
            follow_redirects: {
                type: 'boolean',
                description: 'Follow HTTP redirects',
                default: true
            },
            save_path: {
                type: 'string',
                description: 'Local path to save downloaded content'
            }
        },
        required: ['action']
    };
    async execute(args) {
        try {
            const { action, timeout = 30000, follow_redirects = true } = args;
            switch (action) {
                case 'fetch':
                    return await this.fetchContent(args);
                case 'download':
                    return await this.downloadFile(args);
                case 'search':
                    return await this.searchWeb(args);
                case 'post':
                    return await this.makeRequest('POST', args);
                case 'put':
                    return await this.makeRequest('PUT', args);
                case 'delete':
                    return await this.makeRequest('DELETE', args);
                default:
                    throw new Error(`Unknown action: ${action}`);
            }
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error.message || 'Web operation failed'
            };
        }
    }
    async fetchContent(args) {
        const { url, headers, timeout, follow_redirects } = args;
        if (!url) {
            throw new Error('URL is required for fetch action');
        }
        try {
            const response = await axios_1.default.get(url, {
                headers,
                timeout,
                maxRedirects: follow_redirects ? 5 : 0,
                validateStatus: () => true // Don't throw on HTTP error status
            });
            const contentType = response.headers['content-type'] || '';
            const isText = contentType.includes('text/') ||
                contentType.includes('application/json') ||
                contentType.includes('application/xml');
            return {
                success: response.status >= 200 && response.status < 300,
                output: isText ? response.data : `Binary content (${response.data.length} bytes)`,
                metadata: {
                    url,
                    status: response.status,
                    statusText: response.statusText,
                    headers: response.headers,
                    contentType,
                    size: response.data.length
                }
            };
        }
        catch (error) {
            throw new Error(`Failed to fetch ${url}: ${error.message}`);
        }
    }
    async downloadFile(args) {
        const { url, save_path, headers, timeout } = args;
        if (!url) {
            throw new Error('URL is required for download action');
        }
        if (!save_path) {
            throw new Error('Save path is required for download action');
        }
        try {
            const response = await axios_1.default.get(url, {
                headers,
                timeout,
                responseType: 'stream'
            });
            const fs = require('fs');
            const path = require('path');
            // Ensure directory exists
            const dir = path.dirname(save_path);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            const writer = fs.createWriteStream(save_path);
            response.data.pipe(writer);
            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    const stats = fs.statSync(save_path);
                    resolve({
                        success: true,
                        output: `File downloaded successfully: ${save_path}`,
                        metadata: {
                            url,
                            save_path,
                            size: stats.size,
                            contentType: response.headers['content-type']
                        }
                    });
                });
                writer.on('error', (error) => {
                    reject(new Error(`Failed to save file: ${error.message}`));
                });
            });
        }
        catch (error) {
            throw new Error(`Failed to download ${url}: ${error.message}`);
        }
    }
    async searchWeb(args) {
        const { query, timeout } = args;
        if (!query) {
            throw new Error('Query is required for search action');
        }
        try {
            // Using DuckDuckGo Instant Answer API as an example
            // In a real implementation, you might use Google Custom Search API or other services
            const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`;
            const response = await axios_1.default.get(searchUrl, {
                timeout,
                headers: {
                    'User-Agent': 'Kritrima-AI/1.0'
                }
            });
            const data = response.data;
            let results = '';
            if (data.Abstract) {
                results += `Abstract: ${data.Abstract}\n\n`;
            }
            if (data.RelatedTopics && data.RelatedTopics.length > 0) {
                results += 'Related Topics:\n';
                for (const topic of data.RelatedTopics.slice(0, 5)) {
                    if (topic.Text) {
                        results += `- ${topic.Text}\n`;
                        if (topic.FirstURL) {
                            results += `  URL: ${topic.FirstURL}\n`;
                        }
                    }
                }
            }
            if (data.Results && data.Results.length > 0) {
                results += '\nResults:\n';
                for (const result of data.Results.slice(0, 5)) {
                    results += `- ${result.Text}\n`;
                    if (result.FirstURL) {
                        results += `  URL: ${result.FirstURL}\n`;
                    }
                }
            }
            return {
                success: true,
                output: results || 'No results found',
                metadata: {
                    query,
                    source: 'DuckDuckGo',
                    resultsCount: (data.Results?.length || 0) + (data.RelatedTopics?.length || 0)
                }
            };
        }
        catch (error) {
            throw new Error(`Search failed: ${error.message}`);
        }
    }
    async makeRequest(method, args) {
        const { url, headers, data, timeout } = args;
        if (!url) {
            throw new Error(`URL is required for ${method} request`);
        }
        try {
            const config = {
                method,
                url,
                headers,
                timeout,
                validateStatus: () => true
            };
            if (data && (method === 'POST' || method === 'PUT')) {
                config.data = data;
            }
            const response = await (0, axios_1.default)(config);
            return {
                success: response.status >= 200 && response.status < 300,
                output: typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2),
                metadata: {
                    method,
                    url,
                    status: response.status,
                    statusText: response.statusText,
                    headers: response.headers
                }
            };
        }
        catch (error) {
            throw new Error(`${method} request failed: ${error.message}`);
        }
    }
    // Helper method to check if URL is accessible
    async checkUrl(url) {
        try {
            const response = await axios_1.default.head(url, {
                timeout: 10000,
                validateStatus: () => true
            });
            return {
                success: response.status >= 200 && response.status < 300,
                output: `URL is ${response.status >= 200 && response.status < 300 ? 'accessible' : 'not accessible'}`,
                metadata: {
                    url,
                    status: response.status,
                    statusText: response.statusText,
                    headers: response.headers
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: 'URL is not accessible',
                error: error.message
            };
        }
    }
    // Helper method to extract links from HTML content
    async extractLinks(html) {
        const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>/gi;
        const links = [];
        let match;
        while ((match = linkRegex.exec(html)) !== null) {
            links.push(match[1]);
        }
        return links;
    }
    // Helper method to validate URL format
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.WebTool = WebTool;
//# sourceMappingURL=web.js.map