import { Tool, ToolResult } from '../types';
export declare class FileTool implements Tool {
    name: string;
    description: string;
    parameters: {
        type: "object";
        properties: {
            action: {
                type: string;
                enum: string[];
                description: string;
            };
            path: {
                type: string;
                description: string;
            };
            content: {
                type: string;
                description: string;
            };
            encoding: {
                type: string;
                description: string;
                default: string;
            };
            recursive: {
                type: string;
                description: string;
                default: boolean;
            };
            destination: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
    execute(args: {
        action: string;
        path: string;
        content?: string;
        encoding?: string;
        recursive?: boolean;
        destination?: string;
    }): Promise<ToolResult>;
    private readFile;
    private writeFile;
    private appendFile;
    private deleteFile;
    private fileExists;
    private getFileStat;
    private createDirectory;
    private listDirectory;
    private copyFile;
    private moveFile;
}
//# sourceMappingURL=file.d.ts.map