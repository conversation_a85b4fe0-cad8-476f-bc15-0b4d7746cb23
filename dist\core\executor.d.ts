import { EventEmitter } from 'events';
import { ExecutionPlan, PlanStep } from '../types';
import { ToolManager } from '../tools/manager';
export declare class PlanExecutor extends EventEmitter {
    private toolManager;
    private currentPlan;
    private isExecuting;
    constructor(toolManager: ToolManager);
    executePlan(plan: ExecutionPlan): Promise<void>;
    private executeStep;
    private topologicalSort;
    executeStepsInParallel(steps: PlanStep[]): Promise<void>;
    getCurrentPlan(): ExecutionPlan | null;
    isCurrentlyExecuting(): boolean;
    pauseExecution(): Promise<void>;
    resumeExecution(): Promise<void>;
    cancelExecution(): Promise<void>;
    createPlan(description: string, context: any): ExecutionPlan;
    private breakDownTask;
    private generatePlanId;
    private generateStepId;
    getExecutionStats(plan: ExecutionPlan): {
        total: number;
        completed: number;
        failed: number;
        pending: number;
        executing: number;
    };
    retryFailedSteps(plan: ExecutionPlan): Promise<void>;
    validatePlan(plan: ExecutionPlan): {
        valid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=executor.d.ts.map