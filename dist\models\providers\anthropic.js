"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnthropicProvider = void 0;
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
class AnthropicProvider {
    client;
    constructor(apiKey) {
        this.client = new sdk_1.default({
            apiKey: apiKey
        });
    }
    async generateResponse(messages, tools, config) {
        try {
            const anthropicMessages = this.convertMessages(messages);
            const anthropicTools = this.convertTools(tools);
            // Extract system message
            const systemMessage = messages.find(m => m.role === 'system')?.content || '';
            const userMessages = anthropicMessages;
            const response = await this.client.messages.create({
                model: config.model,
                max_tokens: config.maxTokens || 4096,
                temperature: config.temperature,
                system: systemMessage,
                messages: userMessages,
                tools: anthropicTools.length > 0 ? anthropicTools : undefined,
                tool_choice: anthropicTools.length > 0 ? { type: 'auto' } : undefined
            });
            let content = '';
            let toolCalls = [];
            for (const block of response.content) {
                if (block.type === 'text') {
                    content += block.text;
                }
                else if (block.type === 'tool_use') {
                    toolCalls.push({
                        id: block.id,
                        type: 'function',
                        function: {
                            name: block.name,
                            arguments: JSON.stringify(block.input)
                        }
                    });
                }
            }
            return {
                content,
                done: true,
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined
            };
        }
        catch (error) {
            throw new Error(`Anthropic API error: ${error}`);
        }
    }
    async streamResponse(messages, tools, config, onChunk) {
        try {
            const anthropicMessages = this.convertMessages(messages);
            const anthropicTools = this.convertTools(tools);
            const systemMessage = messages.find(m => m.role === 'system')?.content || '';
            const userMessages = anthropicMessages;
            const stream = await this.client.messages.create({
                model: config.model,
                max_tokens: config.maxTokens || 4096,
                temperature: config.temperature,
                system: systemMessage,
                messages: userMessages,
                tools: anthropicTools.length > 0 ? anthropicTools : undefined,
                tool_choice: anthropicTools.length > 0 ? { type: 'auto' } : undefined,
                stream: true
            });
            for await (const chunk of stream) {
                if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
                    onChunk(chunk.delta.text);
                }
            }
        }
        catch (error) {
            throw new Error(`Anthropic streaming error: ${error}`);
        }
    }
    convertMessages(messages) {
        return messages
            .filter(msg => msg.role !== 'system') // System messages handled separately
            .map(msg => {
            switch (msg.role) {
                case 'user':
                    return {
                        role: 'user',
                        content: msg.content
                    };
                case 'assistant':
                    const content = [];
                    if (msg.content) {
                        content.push({
                            type: 'text',
                            text: msg.content
                        });
                    }
                    if (msg.tool_calls) {
                        for (const toolCall of msg.tool_calls) {
                            content.push({
                                type: 'tool_use',
                                id: toolCall.id,
                                name: toolCall.function.name,
                                input: JSON.parse(toolCall.function.arguments)
                            });
                        }
                    }
                    return {
                        role: 'assistant',
                        content
                    };
                case 'function':
                    return {
                        role: 'user',
                        content: [
                            {
                                type: 'tool_result',
                                tool_use_id: msg.name || 'unknown',
                                content: msg.content
                            }
                        ]
                    };
                default:
                    throw new Error(`Unsupported message role: ${msg.role}`);
            }
        });
    }
    convertTools(tools) {
        return tools.map(tool => ({
            name: tool.name,
            description: tool.description,
            input_schema: tool.parameters
        }));
    }
    async testConnection() {
        try {
            await this.client.messages.create({
                model: 'claude-3-haiku-20240307',
                max_tokens: 10,
                messages: [{ role: 'user', content: 'test' }]
            });
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async getAvailableModels() {
        // Anthropic doesn't have a models endpoint, return known models
        return [
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307'
        ];
    }
}
exports.AnthropicProvider = AnthropicProvider;
//# sourceMappingURL=anthropic.js.map