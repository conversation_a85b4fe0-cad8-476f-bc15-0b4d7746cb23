{"version": 3, "file": "file.js", "sourceRoot": "", "sources": ["../../src/tools/file.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,+BAAiC;AAGjC,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AACxC,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,SAAS,CAAC,CAAC;AAC1C,MAAM,KAAK,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAClC,MAAM,IAAI,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAChC,MAAM,OAAO,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AAEtC,MAAa,QAAQ;IACnB,IAAI,GAAG,MAAM,CAAC;IACd,WAAW,GAAG,2IAA2I,CAAC;IAC1J,UAAU,GAAG;QACX,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;gBAC9F,WAAW,EAAE,+BAA+B;aAC7C;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,4BAA4B;aAC1C;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,6CAA6C;aAC3D;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,+BAA+B;gBAC5C,OAAO,EAAE,MAAM;aAChB;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,4DAA4D;gBACzE,OAAO,EAAE,KAAK;aACf;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,0CAA0C;aACxD;SACF;QACD,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;KAC7B,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,IAOb;QACC,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,GAAG,MAAM,EAAE,SAAS,GAAG,KAAK,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;YAEpG,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAEjD,KAAK,OAAO;oBACV,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAEjE,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAElE,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAEzC,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAEzC,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAE1C,KAAK,OAAO;oBACV,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBAEzD,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAE5C,KAAK,MAAM;oBACT,IAAI,CAAC,WAAW,EAAE,CAAC;wBACjB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;oBAC/D,CAAC;oBACD,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAEpD,KAAK,MAAM;oBACT,IAAI,CAAC,WAAW,EAAE,CAAC;wBACjB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;oBAC/D,CAAC;oBACD,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAEpD;oBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,uBAAuB;aAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,QAAgB;QACvD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,QAAQ,EAAE,QAA0B,CAAC,CAAC;YACrE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,OAAO;gBACf,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,OAAO,CAAC,MAAM;oBACpB,QAAQ;iBACT;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,EAAE;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,OAAe,EAAE,QAAgB;QACzE,IAAI,CAAC;YACH,0BAA0B;YAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,KAAK,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACxC,CAAC;YAED,MAAM,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,QAA0B,CAAC,CAAC;YAC/D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,8BAA8B,QAAQ,EAAE;gBAChD,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,OAAO,CAAC,MAAM;oBACpB,QAAQ;iBACT;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,yBAAyB,KAAK,CAAC,OAAO,EAAE;aAChD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,OAAe,EAAE,QAAgB;QAC1E,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,QAA0B,CAAC,CAAC;YAC5E,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,6BAA6B,QAAQ,EAAE;gBAC/C,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,YAAY,EAAE,OAAO,CAAC,MAAM;oBAC5B,QAAQ;iBACT;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEnC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBACvD,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,sBAAsB,QAAQ,EAAE;oBACxC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE;iBAChD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,iBAAiB,QAAQ,EAAE;oBACnC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;iBAC3C,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,qBAAqB,KAAK,CAAC,OAAO,EAAE;aAC5C,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrB,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,gBAAgB,QAAQ,EAAE;gBAClC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE;aAC3C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,wBAAwB,QAAQ,EAAE;gBAC1C,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE;aAC5C,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;gBACtB,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE;gBAChC,OAAO,EAAE,KAAK,CAAC,SAAS;gBACxB,QAAQ,EAAE,KAAK,CAAC,KAAK;gBACrB,QAAQ,EAAE,KAAK,CAAC,KAAK;gBACrB,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aACpC,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;gBACzC,QAAQ,EAAE,QAAQ;aACnB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,SAAkB;QAC/D,IAAI,CAAC;YACH,MAAM,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YACpC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,sBAAsB,OAAO,EAAE;gBACvC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE;aACvC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,+BAA+B,KAAK,CAAC,OAAO,EAAE;aACtD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAe;QACzC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9D,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAClC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;gBAC/C,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC;aACpC,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;gBACzC,QAAQ,EAAE;oBACR,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,KAAK,CAAC,MAAM;oBACnB,KAAK,EAAE,QAAQ;iBAChB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,UAAkB,EAAE,QAAgB;QACzD,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACjD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,oBAAoB,UAAU,OAAO,QAAQ,EAAE;gBACvD,QAAQ,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE;aACxD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,EAAE;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,UAAkB,EAAE,QAAgB;QACzD,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC/C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,mBAAmB,UAAU,OAAO,QAAQ,EAAE;gBACtD,QAAQ,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE;aACxD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,wBAAwB,KAAK,CAAC,OAAO,EAAE;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AA5TD,4BA4TC"}