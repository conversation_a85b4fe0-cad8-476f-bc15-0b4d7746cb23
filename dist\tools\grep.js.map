{"version": 3, "file": "grep.js", "sourceRoot": "", "sources": ["../../src/tools/grep.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,+BAA4B;AAG5B,MAAa,QAAQ;IACnB,IAAI,GAAG,MAAM,CAAC;IACd,WAAW,GAAG,iIAAiI,CAAC;IAChJ,UAAU,GAAG;QACX,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,mCAAmC;aACjD;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,qCAAqC;gBAClD,OAAO,EAAE,GAAG;aACb;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,mCAAmC;gBAChD,OAAO,EAAE,IAAI;aACd;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,uBAAuB;gBACpC,OAAO,EAAE,KAAK;aACf;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,qCAAqC;gBAClD,OAAO,EAAE,KAAK;aACf;YACD,oBAAoB,EAAE;gBACpB,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,iCAAiC;gBAC9C,OAAO,EAAE,IAAI;aACd;YACD,aAAa,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,gDAAgD;gBAC7D,OAAO,EAAE,CAAC;aACX;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,gDAAgD;gBAC7D,OAAO,EAAE,GAAG;aACb;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACzB,WAAW,EAAE,oCAAoC;gBACjD,OAAO,EAAE,CAAC,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;aACnD;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,qCAAqC;gBAClD,OAAO,EAAE,GAAG;aACb;SACF;QACD,QAAQ,EAAE,CAAC,SAAS,CAAC;KACtB,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,IAWb;QACC,IAAI,CAAC;YACH,MAAM,EACJ,OAAO,EACP,IAAI,EAAE,UAAU,GAAG,GAAG,EACtB,SAAS,GAAG,IAAI,EAChB,cAAc,GAAG,KAAK,EACtB,KAAK,GAAG,KAAK,EACb,oBAAoB,GAAG,IAAI,EAC3B,aAAa,GAAG,CAAC,EACjB,YAAY,GAAG,GAAG,EAClB,YAAY,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EACxD,WAAW,GAAG,GAAG,EAClB,GAAG,IAAI,CAAC;YAET,mBAAmB;YACnB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACtC,IAAI,aAAa,GAAa,EAAE,CAAC;YAEjC,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACnB,aAAa,GAAG,CAAC,UAAU,CAAC,CAAC;YAC/B,CAAC;iBAAM,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC/B,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;YACjG,CAAC;YAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAC5C,aAAa,EACb,OAAO,EACP;gBACE,cAAc;gBACd,KAAK;gBACL,oBAAoB;gBACpB,aAAa;gBACb,WAAW;aACZ,CACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;gBACzC,QAAQ,EAAE;oBACR,OAAO;oBACP,UAAU;oBACV,aAAa,EAAE,aAAa,CAAC,MAAM;oBACnC,YAAY,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;oBACnF,aAAa,EAAE,aAAa,CAAC,MAAM;iBACpC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,eAAe;aACxC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,OAAe,EACf,WAAmB,EACnB,WAAqB,EACrB,SAAkB;QAElB,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,MAAM,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;QAClE,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;QAE9D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAA,WAAI,EAAC,WAAW,EAAE;gBACpC,GAAG,EAAE,OAAO;gBACZ,MAAM,EAAE,cAAc;gBACtB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,SAAS,GAAa,EAAE,CAAC;YAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEzC,0CAA0C;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvD,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;oBACpB,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,KAAe,EACf,OAAe,EACf,OAMC;QAED,MAAM,OAAO,GAAmB,EAAE,CAAC;QACnC,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,YAAY,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxC,MAAM;YACR,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBAEhE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACvB,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI;wBACJ,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,WAAW,GAAG,YAAY,CAAC;qBAC9D,CAAC,CAAC;oBACH,YAAY,IAAI,OAAO,CAAC,MAAM,CAAC;gBACjC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,gCAAgC;gBAChC,SAAS;YACX,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,eAAe,CACrB,OAAe,EACf,OAAe,EACf,OAKC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,OAAO,GAAY,EAAE,CAAC;QAE5B,IAAI,WAAmB,CAAC;QAExB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;YAClD,WAAW,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;YACtE,MAAM,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;YAClD,WAAW,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;YAE3D,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;gBAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;gBAEzE,MAAM,YAAY,GAAa,EAAE,CAAC;gBAClC,KAAK,IAAI,CAAC,GAAG,YAAY,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;oBAChD,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;oBACnC,MAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;oBAChE,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACxD,CAAC;gBAED,OAAO,CAAC,IAAI,CAAC;oBACX,UAAU,EAAE,CAAC,GAAG,CAAC;oBACjB,IAAI;oBACJ,UAAU,EAAE,WAAW,CAAC,MAAM;oBAC9B,OAAO,EAAE,YAAY;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,aAAa,CAAC,OAAuB;QAC3C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,IAAI,MAAM,GAAG,oBAAoB,OAAO,CAAC,MAAM,eAAe,CAAC;QAE/D,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,SAAS,MAAM,CAAC,IAAI,IAAI,CAAC;YACnC,MAAM,IAAI,YAAY,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC;YAChD,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YAEhC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;gBAC5C,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,QAAQ,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC;gBACxD,CAAC;gBACD,MAAM,IAAI,IAAI,CAAC;YACjB,CAAC;YAED,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,iDAAiD;IACjD,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,QAAiB;QACrD,MAAM,QAAQ,GAA2B;YACvC,UAAU,EAAE,kFAAkF;YAC9F,UAAU,EAAE,oGAAoG;YAChH,MAAM,EAAE,6BAA6B;YACrC,IAAI,EAAE,iEAAiE;YACvE,GAAG,EAAE,qCAAqC;YAC1C,OAAO,EAAE,wCAAwC;SAClD,CAAC;QAEF,MAAM,YAAY,GAAG,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC;QAE3D,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YACxB,OAAO;YACP,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,IAAI;YACX,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,OAAO,GAA2B;YACtC,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,KAAK;SACZ,CAAC;QAEF,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC;IACnC,CAAC;CACF;AAjVD,4BAiVC"}