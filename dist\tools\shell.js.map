{"version": 3, "file": "shell.js", "sourceRoot": "", "sources": ["../../src/tools/shell.ts"], "names": [], "mappings": ";;;AAAA,iDAA4C;AAC5C,+BAAiC;AAGjC,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,oBAAI,CAAC,CAAC;AAElC,MAAa,SAAS;IACpB,IAAI,GAAG,OAAO,CAAC;IACf,WAAW,GAAG,sGAAsG,CAAC;IACrH,UAAU,GAAG;QACX,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,8BAA8B;aAC5C;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,8CAA8C;aAC5D;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,0CAA0C;aACxD;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,yCAAyC;aACvD;SACF;QACD,QAAQ,EAAE,CAAC,SAAS,CAAC;KACtB,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,IAKb;QACC,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAEpD,8CAA8C;YAC9C,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,sCAAsC;iBAC9C,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAQ;gBACnB,OAAO;gBACP,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE,cAAc;gBAC3C,QAAQ,EAAE,MAAM;aACjB,CAAC;YAEF,IAAI,GAAG,EAAE,CAAC;gBACR,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;YACpB,CAAC;YAED,IAAI,GAAG,EAAE,CAAC;gBACR,OAAO,CAAC,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;YAC3C,CAAC;YAED,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE7D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,IAAI,+BAA+B,CAAC;gBACnE,QAAQ,EAAE;oBACR,OAAO;oBACP,GAAG,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;oBACzB,QAAQ,EAAE,CAAC;iBACZ;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;gBAC1B,KAAK,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,0BAA0B;gBAClE,QAAQ,EAAE;oBACR,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;oBAC9B,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;iBAC1B;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,IAMxB;QACC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YAEtD,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC;oBACN,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,sCAAsC;iBAC9C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,OAAO,GAAQ,EAAE,CAAC;YAExB,IAAI,GAAG,EAAE,CAAC;gBACR,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;YACpB,CAAC;YAED,IAAI,GAAG,EAAE,CAAC;gBACR,OAAO,CAAC,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;YAC3C,CAAC;YAED,MAAM,KAAK,GAAG,IAAA,qBAAK,EAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC3C,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACxC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC/B,MAAM,IAAI,MAAM,CAAC;gBACjB,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACxC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC;gBAChB,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBACzB,OAAO,CAAC;oBACN,OAAO,EAAE,IAAI,KAAK,CAAC;oBACnB,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,MAAM;oBACb,QAAQ,EAAE;wBACR,OAAO;wBACP,GAAG,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;wBACzB,QAAQ,EAAE,IAAI,IAAI,CAAC;qBACpB;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,OAAO,CAAC;oBACN,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,QAAQ,EAAE;wBACR,OAAO;wBACP,GAAG,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;wBACzB,QAAQ,EAAE,CAAC;qBACZ;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACxC,MAAM,iBAAiB,GAAG;YACxB,eAAe,EAAE,WAAW;YAC5B,eAAe,EAAE,WAAW;YAC5B,aAAa,EAAE,YAAY;YAC3B,wBAAwB,EAAE,gBAAgB;YAC1C,UAAU,EAAE,oBAAoB;YAChC,QAAQ,EAAE,kBAAkB;YAC5B,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,wBAAwB;YAChC,oBAAoB,EAAE,eAAe;YACrC,eAAe,EAAE,YAAY;YAC7B,eAAe,EAAE,YAAY;YAC7B,eAAe,EAAE,YAAY;YAC7B,kBAAkB,EAAE,cAAc;YAClC,WAAW,EAAE,gBAAgB;YAC7B,WAAW,EAAE,UAAU;YACvB,WAAW,EAAE,UAAU;SACxB,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAChF,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO;YAC3C,CAAC,CAAC,CAAC,YAAY,EAAE,6BAA6B,CAAC;YAC/C,CAAC,CAAC,CAAC,UAAU,EAAE,uCAAuC,CAAC,CAAC;QAE1D,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;gBACpD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,IAAI,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,MAAM,CAAC;gBAC5C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,6BAA6B;YAC/B,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,MAAM,IAAI,kCAAkC;YACpD,QAAQ,EAAE;gBACR,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,WAAW,EAAE,OAAO,CAAC,OAAO;aAC7B;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC;QACrE,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAC5D,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,SAAkB;QAChC,MAAM,GAAG,GAAG,SAAS,IAAI,GAAG,CAAC;QAC7B,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO;YAC1C,CAAC,CAAC,QAAQ,GAAG,GAAG;YAChB,CAAC,CAAC,WAAW,GAAG,GAAG,CAAC;QAEtB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC;IACzD,CAAC;CACF;AAlOD,8BAkOC"}