import { AIModel, ModelConfig, Message, ContextFile, Tool, StreamingResponse } from '../types';
export declare class ModelManager {
    private providers;
    private models;
    private defaultModel;
    constructor();
    private initializeProviders;
    private loadAvailableModels;
    getAvailableModels(): Promise<AIModel[]>;
    getModel(modelName: string): Promise<AIModel>;
    getDefaultModel(): Promise<AIModel>;
    getDefaultConfig(): Promise<ModelConfig>;
    getConfigForModel(modelName: string): Promise<ModelConfig>;
    generateResponse(messages: Message[], context: ContextFile[], tools: Tool[], config: ModelConfig): Promise<StreamingResponse>;
    private prepareContext;
    private buildSystemMessage;
    streamResponse(messages: Message[], context: ContextFile[], tools: Tool[], config: ModelConfig, onChunk: (chunk: string) => void): Promise<void>;
}
//# sourceMappingURL=manager.d.ts.map