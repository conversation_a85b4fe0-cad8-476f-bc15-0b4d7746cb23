"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OllamaProvider = void 0;
const axios_1 = __importDefault(require("axios"));
class OllamaProvider {
    baseUrl;
    constructor(baseUrl = 'http://localhost:11434') {
        this.baseUrl = baseUrl;
    }
    async generateResponse(messages, tools, config) {
        try {
            const ollamaMessages = this.convertMessages(messages);
            const ollamaTools = this.convertTools(tools);
            const response = await axios_1.default.post(`${this.baseUrl}/api/chat`, {
                model: config.model,
                messages: ollamaMessages,
                tools: ollamaTools.length > 0 ? ollamaTools : undefined,
                options: {
                    temperature: config.temperature,
                    top_p: config.topP,
                    num_predict: config.maxTokens
                },
                stream: false
            });
            const data = response.data;
            // Parse tool calls if present
            let toolCalls = [];
            if (data.message?.tool_calls) {
                toolCalls = data.message.tool_calls.map((tc) => ({
                    id: tc.function?.name || Math.random().toString(36),
                    type: 'function',
                    function: {
                        name: tc.function?.name,
                        arguments: JSON.stringify(tc.function?.arguments || {})
                    }
                }));
            }
            return {
                content: data.message?.content || '',
                done: true,
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined
            };
        }
        catch (error) {
            throw new Error(`Ollama API error: ${error}`);
        }
    }
    async streamResponse(messages, tools, config, onChunk) {
        try {
            const ollamaMessages = this.convertMessages(messages);
            const ollamaTools = this.convertTools(tools);
            const response = await axios_1.default.post(`${this.baseUrl}/api/chat`, {
                model: config.model,
                messages: ollamaMessages,
                tools: ollamaTools.length > 0 ? ollamaTools : undefined,
                options: {
                    temperature: config.temperature,
                    top_p: config.topP,
                    num_predict: config.maxTokens
                },
                stream: true
            }, {
                responseType: 'stream'
            });
            response.data.on('data', (chunk) => {
                const lines = chunk.toString().split('\n').filter(line => line.trim());
                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);
                        if (data.message?.content) {
                            onChunk(data.message.content);
                        }
                    }
                    catch (parseError) {
                        // Ignore parsing errors for incomplete chunks
                    }
                }
            });
            return new Promise((resolve, reject) => {
                response.data.on('end', resolve);
                response.data.on('error', reject);
            });
        }
        catch (error) {
            throw new Error(`Ollama streaming error: ${error}`);
        }
    }
    convertMessages(messages) {
        return messages.map(msg => {
            switch (msg.role) {
                case 'system':
                    return {
                        role: 'system',
                        content: msg.content
                    };
                case 'user':
                    return {
                        role: 'user',
                        content: msg.content
                    };
                case 'assistant':
                    const assistantMsg = {
                        role: 'assistant',
                        content: msg.content
                    };
                    if (msg.tool_calls) {
                        assistantMsg.tool_calls = msg.tool_calls.map(tc => ({
                            function: {
                                name: tc.function.name,
                                arguments: JSON.parse(tc.function.arguments)
                            }
                        }));
                    }
                    return assistantMsg;
                case 'function':
                    return {
                        role: 'tool',
                        content: msg.content
                    };
                default:
                    throw new Error(`Unsupported message role: ${msg.role}`);
            }
        });
    }
    convertTools(tools) {
        return tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters
            }
        }));
    }
    async testConnection() {
        try {
            await axios_1.default.get(`${this.baseUrl}/api/tags`);
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async getAvailableModels() {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/api/tags`);
            return response.data.models?.map((model) => model.name) || [];
        }
        catch (error) {
            return ['llama2', 'codellama', 'mistral']; // fallback
        }
    }
    async pullModel(modelName) {
        try {
            await axios_1.default.post(`${this.baseUrl}/api/pull`, {
                name: modelName
            });
        }
        catch (error) {
            throw new Error(`Failed to pull model ${modelName}: ${error}`);
        }
    }
    async deleteModel(modelName) {
        try {
            await axios_1.default.delete(`${this.baseUrl}/api/delete`, {
                data: { name: modelName }
            });
        }
        catch (error) {
            throw new Error(`Failed to delete model ${modelName}: ${error}`);
        }
    }
}
exports.OllamaProvider = OllamaProvider;
//# sourceMappingURL=ollama.js.map