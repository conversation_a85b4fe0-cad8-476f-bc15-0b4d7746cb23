"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIInterface = void 0;
const readline = __importStar(require("readline"));
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
const events_1 = require("events");
const manager_1 = require("../context/manager");
const manager_2 = require("../models/manager");
const manager_3 = require("../tools/manager");
const executor_1 = require("../core/executor");
const error_detector_1 = require("../core/error-detector");
const diff_reviewer_1 = require("../core/diff-reviewer");
class CLIInterface extends events_1.EventEmitter {
    rl;
    contextManager;
    modelManager;
    toolManager;
    planExecutor;
    errorDetector;
    diffReviewer;
    currentSession = null;
    config;
    spinner;
    constructor(config) {
        super();
        this.config = config;
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: chalk_1.default.cyan('kritrima> ')
        });
        this.contextManager = new manager_1.ContextManager(config.contextSize);
        this.modelManager = new manager_2.ModelManager();
        this.toolManager = new manager_3.ToolManager();
        this.planExecutor = new executor_1.PlanExecutor(this.toolManager);
        this.errorDetector = new error_detector_1.ErrorDetector(config.workingDirectory);
        this.diffReviewer = new diff_reviewer_1.DiffReviewer();
        this.spinner = (0, ora_1.default)();
        this.setupEventHandlers();
        this.initializeSession();
    }
    setupEventHandlers() {
        this.rl.on('line', this.handleUserInput.bind(this));
        this.rl.on('close', this.handleExit.bind(this));
        this.planExecutor.on('stepStart', this.handleStepStart.bind(this));
        this.planExecutor.on('stepComplete', this.handleStepComplete.bind(this));
        this.planExecutor.on('planComplete', this.handlePlanComplete.bind(this));
        this.errorDetector.on('errorDetected', this.handleErrorDetected.bind(this));
    }
    async initializeSession() {
        this.currentSession = {
            id: this.generateSessionId(),
            messages: [],
            context: [],
            model: await this.modelManager.getDefaultModel(),
            config: await this.modelManager.getDefaultConfig(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        // Load initial context from working directory
        await this.contextManager.loadWorkingDirectory(this.config.workingDirectory);
        this.currentSession.context = this.contextManager.getContext();
    }
    start() {
        this.displayWelcome();
        this.displayStatus();
        this.rl.prompt();
    }
    displayWelcome() {
        console.log(chalk_1.default.bold.blue('\n🤖 Kritrima AI - Advanced Local CLI Terminal LLM Environment'));
        console.log(chalk_1.default.gray('Type "help" for commands, "exit" to quit\n'));
    }
    displayStatus() {
        const contextFiles = this.currentSession?.context.length || 0;
        const totalTokens = this.contextManager.getTotalTokens();
        const model = this.currentSession?.model.name || 'none';
        console.log(chalk_1.default.dim(`Context: ${contextFiles} files (${totalTokens} tokens) | Model: ${model}`));
    }
    async handleUserInput(input) {
        const trimmedInput = input.trim();
        if (!trimmedInput) {
            this.rl.prompt();
            return;
        }
        // Handle special commands
        if (await this.handleCommand(trimmedInput)) {
            this.rl.prompt();
            return;
        }
        // Process as AI query
        await this.processAIQuery(trimmedInput);
        this.rl.prompt();
    }
    async handleCommand(input) {
        const [command, ...args] = input.split(' ');
        switch (command.toLowerCase()) {
            case 'help':
                this.displayHelp();
                return true;
            case 'status':
                this.displayStatus();
                return true;
            case 'models':
                await this.displayModels();
                return true;
            case 'model':
                if (args.length > 0) {
                    await this.switchModel(args[0]);
                }
                else {
                    console.log(chalk_1.default.yellow('Usage: model <model-name>'));
                }
                return true;
            case 'context':
                await this.handleContextCommand(args);
                return true;
            case 'scan':
                await this.scanForErrors();
                return true;
            case 'clear':
                this.clearSession();
                return true;
            case 'exit':
            case 'quit':
                this.handleExit();
                return true;
            default:
                return false;
        }
    }
    displayHelp() {
        console.log(chalk_1.default.bold('\nAvailable Commands:'));
        console.log(chalk_1.default.cyan('  help') + '                 - Show this help message');
        console.log(chalk_1.default.cyan('  status') + '               - Show current status');
        console.log(chalk_1.default.cyan('  models') + '               - List available models');
        console.log(chalk_1.default.cyan('  model <name>') + '         - Switch to a different model');
        console.log(chalk_1.default.cyan('  context add <path>') + '   - Add file/directory to context');
        console.log(chalk_1.default.cyan('  context remove <path>') + ' - Remove file from context');
        console.log(chalk_1.default.cyan('  context list') + '         - List files in context');
        console.log(chalk_1.default.cyan('  scan') + '                 - Scan for errors in working directory');
        console.log(chalk_1.default.cyan('  clear') + '                - Clear current session');
        console.log(chalk_1.default.cyan('  exit/quit') + '            - Exit the application\n');
    }
    async processAIQuery(query) {
        if (!this.currentSession) {
            console.log(chalk_1.default.red('No active session. Please restart the application.'));
            return;
        }
        try {
            this.spinner.start('Processing query...');
            // Add user message to session
            const userMessage = {
                role: 'user',
                content: query
            };
            this.currentSession.messages.push(userMessage);
            // Get AI response with function calling
            const response = await this.modelManager.generateResponse(this.currentSession.messages, this.currentSession.context, this.toolManager.getAvailableTools(), this.currentSession.config);
            this.spinner.stop();
            // Handle streaming response
            if (response.toolCalls && response.toolCalls.length > 0) {
                await this.handleToolCalls(response.toolCalls);
            }
            else {
                console.log(chalk_1.default.green('\n' + response.content));
            }
            // Add assistant message to session
            const assistantMessage = {
                role: 'assistant',
                content: response.content,
                tool_calls: response.toolCalls
            };
            this.currentSession.messages.push(assistantMessage);
            this.currentSession.updatedAt = new Date();
        }
        catch (error) {
            this.spinner.stop();
            console.log(chalk_1.default.red(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    }
    async handleToolCalls(toolCalls) {
        console.log(chalk_1.default.yellow('\n🔧 Executing tools autonomously...\n'));
        for (const toolCall of toolCalls) {
            try {
                const tool = this.toolManager.getTool(toolCall.function.name);
                if (!tool) {
                    console.log(chalk_1.default.red(`Unknown tool: ${toolCall.function.name}`));
                    continue;
                }
                console.log(chalk_1.default.blue(`▶ ${tool.name}: ${tool.description}`));
                const args = JSON.parse(toolCall.function.arguments);
                const result = await tool.execute(args);
                if (result.success) {
                    console.log(chalk_1.default.green(`✓ ${result.output}`));
                }
                else {
                    console.log(chalk_1.default.red(`✗ ${result.error || 'Tool execution failed'}`));
                }
                // Add tool result to session
                const toolMessage = {
                    role: 'function',
                    name: toolCall.function.name,
                    content: JSON.stringify(result)
                };
                this.currentSession?.messages.push(toolMessage);
            }
            catch (error) {
                console.log(chalk_1.default.red(`Error executing ${toolCall.function.name}: ${error}`));
            }
        }
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    handleExit() {
        console.log(chalk_1.default.blue('\nGoodbye! 👋'));
        process.exit(0);
    }
    async displayModels() {
        const models = await this.modelManager.getAvailableModels();
        console.log(chalk_1.default.bold('\nAvailable Models:'));
        for (const model of models) {
            const current = model.name === this.currentSession?.model.name ? chalk_1.default.green('(current)') : '';
            console.log(`  ${chalk_1.default.cyan(model.name)} - ${model.provider} ${current}`);
        }
        console.log();
    }
    async switchModel(modelName) {
        try {
            const model = await this.modelManager.getModel(modelName);
            if (this.currentSession) {
                this.currentSession.model = model;
                this.currentSession.config = await this.modelManager.getConfigForModel(modelName);
                console.log(chalk_1.default.green(`Switched to model: ${modelName}`));
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(`Failed to switch model: ${error}`));
        }
    }
    async handleContextCommand(args) {
        if (args.length === 0) {
            console.log(chalk_1.default.yellow('Usage: context <add|remove|list> [path]'));
            return;
        }
        const [action, path] = args;
        switch (action.toLowerCase()) {
            case 'add':
                if (path) {
                    await this.addToContext(path);
                }
                else {
                    console.log(chalk_1.default.yellow('Usage: context add <path>'));
                }
                break;
            case 'remove':
                if (path) {
                    this.removeFromContext(path);
                }
                else {
                    console.log(chalk_1.default.yellow('Usage: context remove <path>'));
                }
                break;
            case 'list':
                this.listContext();
                break;
            default:
                console.log(chalk_1.default.yellow('Unknown context action. Use: add, remove, or list'));
        }
    }
    async addToContext(path) {
        try {
            await this.contextManager.addFile(path);
            if (this.currentSession) {
                this.currentSession.context = this.contextManager.getContext();
            }
            console.log(chalk_1.default.green(`Added to context: ${path}`));
        }
        catch (error) {
            console.log(chalk_1.default.red(`Failed to add to context: ${error}`));
        }
    }
    removeFromContext(path) {
        this.contextManager.removeFile(path);
        if (this.currentSession) {
            this.currentSession.context = this.contextManager.getContext();
        }
        console.log(chalk_1.default.green(`Removed from context: ${path}`));
    }
    listContext() {
        const context = this.contextManager.getContext();
        console.log(chalk_1.default.bold('\nContext Files:'));
        if (context.length === 0) {
            console.log(chalk_1.default.dim('  No files in context'));
        }
        else {
            for (const file of context) {
                console.log(`  ${chalk_1.default.cyan(file.path)} (${file.size} bytes)`);
            }
        }
        console.log();
    }
    async scanForErrors() {
        this.spinner.start('Scanning for errors...');
        try {
            const errors = await this.errorDetector.scanDirectory(this.config.workingDirectory);
            this.spinner.stop();
            if (errors.length === 0) {
                console.log(chalk_1.default.green('✓ No errors detected'));
            }
            else {
                console.log(chalk_1.default.yellow(`Found ${errors.length} issues:`));
                for (const error of errors) {
                    const severity = error.severity === 'error' ? chalk_1.default.red('ERROR') :
                        error.severity === 'warning' ? chalk_1.default.yellow('WARN') :
                            chalk_1.default.blue('INFO');
                    console.log(`  ${severity} ${error.file}:${error.line} - ${error.message}`);
                }
                if (this.config.autoFix) {
                    console.log(chalk_1.default.blue('\nAttempting automatic fixes...'));
                    await this.autoFixErrors(errors);
                }
            }
        }
        catch (error) {
            this.spinner.stop();
            console.log(chalk_1.default.red(`Error scanning: ${error}`));
        }
    }
    async autoFixErrors(errors) {
        const fixableErrors = errors.filter(e => e.fixable);
        if (fixableErrors.length === 0) {
            console.log(chalk_1.default.yellow('No automatically fixable errors found'));
            return;
        }
        for (const error of fixableErrors) {
            try {
                await this.errorDetector.fixError(error);
                console.log(chalk_1.default.green(`✓ Fixed: ${error.message}`));
            }
            catch (fixError) {
                console.log(chalk_1.default.red(`✗ Failed to fix: ${error.message}`));
            }
        }
    }
    clearSession() {
        if (this.currentSession) {
            this.currentSession.messages = [];
            console.log(chalk_1.default.green('Session cleared'));
        }
    }
    handleStepStart(step) {
        console.log(chalk_1.default.blue(`▶ ${step.description}`));
    }
    handleStepComplete(step) {
        if (step.result?.success) {
            console.log(chalk_1.default.green(`✓ ${step.description}`));
        }
        else {
            console.log(chalk_1.default.red(`✗ ${step.description}: ${step.result?.error}`));
        }
    }
    handlePlanComplete(plan) {
        const completed = plan.steps.filter(s => s.status === 'completed').length;
        const total = plan.steps.length;
        console.log(chalk_1.default.green(`\n🎉 Plan completed: ${completed}/${total} steps successful`));
    }
    handleErrorDetected(error) {
        console.log(chalk_1.default.yellow(`\n⚠ Error detected: ${error.file}:${error.line} - ${error.message}`));
    }
}
exports.CLIInterface = CLIInterface;
//# sourceMappingURL=interface.js.map