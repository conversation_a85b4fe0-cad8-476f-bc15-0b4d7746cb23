export interface AIModel {
    name: string;
    provider: 'openai' | 'anthropic' | 'deepseek' | 'ollama';
    maxTokens: number;
    supportsStreaming: boolean;
    supportsFunctionCalling: boolean;
}
export interface ModelConfig {
    apiKey?: string;
    baseUrl?: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
}
export interface Message {
    role: 'system' | 'user' | 'assistant' | 'function';
    content: string;
    name?: string;
    function_call?: FunctionCall;
    tool_calls?: ToolCall[];
}
export interface FunctionCall {
    name: string;
    arguments: string;
}
export interface ToolCall {
    id: string;
    type: 'function';
    function: FunctionCall;
}
export interface Tool {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
    execute: (args: any) => Promise<ToolResult>;
}
export interface ToolResult {
    success: boolean;
    output: string;
    error?: string;
    metadata?: Record<string, any>;
}
export interface ContextFile {
    path: string;
    content: string;
    size: number;
    lastModified: Date;
    language?: string;
}
export interface ContextManager {
    addFile(path: string): Promise<void>;
    removeFile(path: string): void;
    getContext(): ContextFile[];
    getTotalTokens(): number;
    optimize(): void;
}
export interface ChatSession {
    id: string;
    messages: Message[];
    context: ContextFile[];
    model: AIModel;
    config: ModelConfig;
    createdAt: Date;
    updatedAt: Date;
}
export interface ExecutionPlan {
    id: string;
    steps: PlanStep[];
    status: 'pending' | 'executing' | 'completed' | 'failed';
    createdAt: Date;
}
export interface PlanStep {
    id: string;
    description: string;
    tool: string;
    args: any;
    status: 'pending' | 'executing' | 'completed' | 'failed';
    result?: ToolResult;
    dependencies?: string[];
}
export interface DiffChange {
    type: 'add' | 'remove' | 'modify';
    path: string;
    content: string;
    originalContent?: string;
    lineNumber?: number;
}
export interface ReviewSandbox {
    id: string;
    changes: DiffChange[];
    status: 'pending' | 'approved' | 'rejected';
    createdAt: Date;
}
export interface ErrorDetection {
    file: string;
    line: number;
    column: number;
    severity: 'error' | 'warning' | 'info';
    message: string;
    rule?: string;
    fixable: boolean;
}
export interface CLIConfig {
    defaultModel: string;
    autoFix: boolean;
    contextSize: number;
    autonomyLevel: 'full' | 'guided' | 'manual';
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    workingDirectory: string;
}
export interface StreamingResponse {
    content: string;
    done: boolean;
    toolCalls?: ToolCall[];
}
//# sourceMappingURL=index.d.ts.map