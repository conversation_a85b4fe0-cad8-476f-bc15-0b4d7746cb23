"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WriteTool = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class WriteTool {
    name = 'write';
    description = 'Create new files or completely overwrite existing files with new content. Supports various file types and encodings.';
    parameters = {
        type: 'object',
        properties: {
            path: {
                type: 'string',
                description: 'The file path to write to'
            },
            content: {
                type: 'string',
                description: 'The content to write to the file'
            },
            encoding: {
                type: 'string',
                description: 'File encoding (default: utf8)',
                default: 'utf8'
            },
            mode: {
                type: 'string',
                enum: ['write', 'append', 'create'],
                description: 'Write mode: write (overwrite), append, or create (fail if exists)',
                default: 'write'
            },
            create_dirs: {
                type: 'boolean',
                description: 'Create parent directories if they don\'t exist',
                default: true
            },
            backup: {
                type: 'boolean',
                description: 'Create backup if file exists (for write mode)',
                default: false
            }
        },
        required: ['path', 'content']
    };
    async execute(args) {
        try {
            const { path: filePath, content, encoding = 'utf8', mode = 'write', create_dirs = true, backup = false } = args;
            // Validate file path
            if (!filePath || filePath.trim() === '') {
                throw new Error('File path cannot be empty');
            }
            // Check if file exists
            const fileExists = fs.existsSync(filePath);
            // Handle different modes
            switch (mode) {
                case 'create':
                    if (fileExists) {
                        return {
                            success: false,
                            output: '',
                            error: `File already exists: ${filePath}`
                        };
                    }
                    break;
                case 'write':
                    if (fileExists && backup) {
                        await this.createBackup(filePath);
                    }
                    break;
                case 'append':
                    return await this.appendToFile(filePath, content, encoding, create_dirs);
            }
            // Create parent directories if needed
            if (create_dirs) {
                const dir = path.dirname(filePath);
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
            }
            // Write the file
            fs.writeFileSync(filePath, content, encoding);
            // Get file stats
            const stats = fs.statSync(filePath);
            return {
                success: true,
                output: `File written successfully: ${filePath}`,
                metadata: {
                    path: filePath,
                    size: stats.size,
                    encoding,
                    mode,
                    created: !fileExists,
                    lines: content.split('\n').length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error.message || 'Failed to write file'
            };
        }
    }
    async appendToFile(filePath, content, encoding, create_dirs) {
        try {
            // Create parent directories if needed
            if (create_dirs) {
                const dir = path.dirname(filePath);
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
            }
            // Append to file
            fs.appendFileSync(filePath, content, encoding);
            // Get file stats
            const stats = fs.statSync(filePath);
            return {
                success: true,
                output: `Content appended to file: ${filePath}`,
                metadata: {
                    path: filePath,
                    size: stats.size,
                    encoding,
                    mode: 'append',
                    appendedBytes: Buffer.byteLength(content, encoding)
                }
            };
        }
        catch (error) {
            throw new Error(`Failed to append to file: ${error.message}`);
        }
    }
    async createBackup(filePath) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = `${filePath}.backup.${timestamp}`;
        fs.copyFileSync(filePath, backupPath);
        return backupPath;
    }
    // Helper method to write multiple files
    async writeMultipleFiles(files) {
        const results = [];
        let successCount = 0;
        let errorCount = 0;
        for (const file of files) {
            try {
                const result = await this.execute({
                    path: file.path,
                    content: file.content,
                    encoding: file.encoding
                });
                results.push({
                    path: file.path,
                    success: result.success,
                    error: result.error
                });
                if (result.success) {
                    successCount++;
                }
                else {
                    errorCount++;
                }
            }
            catch (error) {
                results.push({
                    path: file.path,
                    success: false,
                    error: error.message
                });
                errorCount++;
            }
        }
        return {
            success: errorCount === 0,
            output: `Wrote ${successCount} files successfully, ${errorCount} failed`,
            metadata: {
                totalFiles: files.length,
                successCount,
                errorCount,
                results
            }
        };
    }
    // Helper method to write from template
    async writeFromTemplate(templatePath, outputPath, variables) {
        try {
            if (!fs.existsSync(templatePath)) {
                throw new Error(`Template file not found: ${templatePath}`);
            }
            let template = fs.readFileSync(templatePath, 'utf8');
            // Replace variables in template
            for (const [key, value] of Object.entries(variables)) {
                const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
                template = template.replace(regex, value);
            }
            return await this.execute({
                path: outputPath,
                content: template
            });
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to write from template: ${error.message}`
            };
        }
    }
    // Helper method to write JSON
    async writeJSON(filePath, data, pretty = true) {
        try {
            const content = pretty ? JSON.stringify(data, null, 2) : JSON.stringify(data);
            return await this.execute({
                path: filePath,
                content
            });
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to write JSON: ${error.message}`
            };
        }
    }
    // Helper method to write CSV
    async writeCSV(filePath, data, headers) {
        try {
            let content = '';
            if (headers) {
                content += headers.join(',') + '\n';
            }
            for (const row of data) {
                content += row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',') + '\n';
            }
            return await this.execute({
                path: filePath,
                content
            });
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to write CSV: ${error.message}`
            };
        }
    }
}
exports.WriteTool = WriteTool;
//# sourceMappingURL=write.js.map