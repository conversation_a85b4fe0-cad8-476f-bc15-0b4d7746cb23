"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrepTool = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const glob_1 = require("glob");
class GrepTool {
    name = 'grep';
    description = 'Search for patterns in files and directories. Supports regex patterns, case-insensitive search, and recursive directory search.';
    parameters = {
        type: 'object',
        properties: {
            pattern: {
                type: 'string',
                description: 'The search pattern (can be regex)'
            },
            path: {
                type: 'string',
                description: 'File or directory path to search in',
                default: '.'
            },
            recursive: {
                type: 'boolean',
                description: 'Search recursively in directories',
                default: true
            },
            case_sensitive: {
                type: 'boolean',
                description: 'Case sensitive search',
                default: false
            },
            regex: {
                type: 'boolean',
                description: 'Treat pattern as regular expression',
                default: false
            },
            include_line_numbers: {
                type: 'boolean',
                description: 'Include line numbers in results',
                default: true
            },
            context_lines: {
                type: 'number',
                description: 'Number of context lines to show around matches',
                default: 0
            },
            file_pattern: {
                type: 'string',
                description: 'File pattern to include (e.g., "*.js", "*.py")',
                default: '*'
            },
            exclude_dirs: {
                type: 'array',
                items: { type: 'string' },
                description: 'Directories to exclude from search',
                default: ['node_modules', '.git', 'dist', 'build']
            },
            max_results: {
                type: 'number',
                description: 'Maximum number of results to return',
                default: 100
            }
        },
        required: ['pattern']
    };
    async execute(args) {
        try {
            const { pattern, path: searchPath = '.', recursive = true, case_sensitive = false, regex = false, include_line_numbers = true, context_lines = 0, file_pattern = '*', exclude_dirs = ['node_modules', '.git', 'dist', 'build'], max_results = 100 } = args;
            // Validate pattern
            if (!pattern || pattern.trim() === '') {
                throw new Error('Search pattern cannot be empty');
            }
            // Check if path exists
            if (!fs.existsSync(searchPath)) {
                throw new Error(`Path does not exist: ${searchPath}`);
            }
            const stats = fs.statSync(searchPath);
            let filesToSearch = [];
            if (stats.isFile()) {
                filesToSearch = [searchPath];
            }
            else if (stats.isDirectory()) {
                filesToSearch = await this.getFilesToSearch(searchPath, file_pattern, exclude_dirs, recursive);
            }
            const searchResults = await this.searchInFiles(filesToSearch, pattern, {
                case_sensitive,
                regex,
                include_line_numbers,
                context_lines,
                max_results
            });
            return {
                success: true,
                output: this.formatResults(searchResults),
                metadata: {
                    pattern,
                    searchPath,
                    filesSearched: filesToSearch.length,
                    totalMatches: searchResults.reduce((sum, result) => sum + result.matches.length, 0),
                    matchingFiles: searchResults.length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error.message || 'Search failed'
            };
        }
    }
    async getFilesToSearch(dirPath, filePattern, excludeDirs, recursive) {
        const globPattern = recursive ? `**/${filePattern}` : filePattern;
        const ignorePatterns = excludeDirs.map(dir => `**/${dir}/**`);
        try {
            const files = await (0, glob_1.glob)(globPattern, {
                cwd: dirPath,
                ignore: ignorePatterns,
                absolute: true,
                nodir: true
            });
            // Filter out binary files
            const textFiles = [];
            for (const file of files) {
                if (await this.isTextFile(file)) {
                    textFiles.push(file);
                }
            }
            return textFiles;
        }
        catch (error) {
            throw new Error(`Failed to get files to search: ${error}`);
        }
    }
    async isTextFile(filePath) {
        try {
            const buffer = fs.readFileSync(filePath);
            // Check for null bytes (binary indicator)
            for (let i = 0; i < Math.min(buffer.length, 1024); i++) {
                if (buffer[i] === 0) {
                    return false;
                }
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async searchInFiles(files, pattern, options) {
        const results = [];
        let totalMatches = 0;
        for (const file of files) {
            if (totalMatches >= options.max_results) {
                break;
            }
            try {
                const content = fs.readFileSync(file, 'utf8');
                const matches = this.searchInContent(content, pattern, options);
                if (matches.length > 0) {
                    results.push({
                        file,
                        matches: matches.slice(0, options.max_results - totalMatches)
                    });
                    totalMatches += matches.length;
                }
            }
            catch (error) {
                // Skip files that can't be read
                continue;
            }
        }
        return results;
    }
    searchInContent(content, pattern, options) {
        const lines = content.split('\n');
        const matches = [];
        let searchRegex;
        if (options.regex) {
            const flags = options.case_sensitive ? 'g' : 'gi';
            searchRegex = new RegExp(pattern, flags);
        }
        else {
            const escapedPattern = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const flags = options.case_sensitive ? 'g' : 'gi';
            searchRegex = new RegExp(escapedPattern, flags);
        }
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineMatches = Array.from(line.matchAll(searchRegex));
            if (lineMatches.length > 0) {
                const contextStart = Math.max(0, i - options.context_lines);
                const contextEnd = Math.min(lines.length - 1, i + options.context_lines);
                const contextLines = [];
                for (let j = contextStart; j <= contextEnd; j++) {
                    const prefix = j === i ? '>' : ' ';
                    const lineNum = options.include_line_numbers ? `${j + 1}:` : '';
                    contextLines.push(`${prefix} ${lineNum} ${lines[j]}`);
                }
                matches.push({
                    lineNumber: i + 1,
                    line,
                    matchCount: lineMatches.length,
                    context: contextLines
                });
            }
        }
        return matches;
    }
    formatResults(results) {
        if (results.length === 0) {
            return 'No matches found.';
        }
        let output = `Found matches in ${results.length} file(s):\n\n`;
        for (const result of results) {
            output += `File: ${result.file}\n`;
            output += `Matches: ${result.matches.length}\n`;
            output += '─'.repeat(50) + '\n';
            for (const match of result.matches) {
                if (match.context.length > 0) {
                    output += match.context.join('\n') + '\n';
                }
                else {
                    output += `Line ${match.lineNumber}: ${match.line}\n`;
                }
                output += '\n';
            }
            output += '\n';
        }
        return output;
    }
    // Helper method for finding function definitions
    async findFunctions(filePath, language) {
        const patterns = {
            javascript: '(function\\s+\\w+|\\w+\\s*=\\s*function|\\w+\\s*\\([^)]*\\)\\s*=>|class\\s+\\w+)',
            typescript: '(function\\s+\\w+|\\w+\\s*=\\s*function|\\w+\\s*\\([^)]*\\)\\s*=>|class\\s+\\w+|interface\\s+\\w+)',
            python: '(def\\s+\\w+|class\\s+\\w+)',
            java: '(public|private|protected)?\\s*(static)?\\s*\\w+\\s+\\w+\\s*\\(',
            cpp: '(\\w+\\s+\\w+\\s*\\(|class\\s+\\w+)',
            default: '(function|def|class|interface)\\s+\\w+'
        };
        const detectedLang = language || this.detectLanguage(filePath);
        const pattern = patterns[detectedLang] || patterns.default;
        return await this.execute({
            pattern,
            path: filePath,
            regex: true,
            include_line_numbers: true
        });
    }
    detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const langMap = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'cpp'
        };
        return langMap[ext] || 'default';
    }
}
exports.GrepTool = GrepTool;
//# sourceMappingURL=grep.js.map