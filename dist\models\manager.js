"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelManager = void 0;
const openai_1 = require("./providers/openai");
const anthropic_1 = require("./providers/anthropic");
const deepseek_1 = require("./providers/deepseek");
const ollama_1 = require("./providers/ollama");
class ModelManager {
    providers = new Map();
    models = [];
    defaultModel = 'gpt-4-turbo-preview';
    constructor() {
        this.initializeProviders();
        this.loadAvailableModels();
    }
    initializeProviders() {
        // Initialize providers with API keys from environment
        if (process.env.OPENAI_API_KEY) {
            this.providers.set('openai', new openai_1.OpenAIProvider(process.env.OPENAI_API_KEY));
        }
        if (process.env.ANTHROPIC_API_KEY) {
            this.providers.set('anthropic', new anthropic_1.AnthropicProvider(process.env.ANTHROPIC_API_KEY));
        }
        if (process.env.DEEPSEEK_API_KEY) {
            this.providers.set('deepseek', new deepseek_1.DeepseekProvider(process.env.DEEPSEEK_API_KEY));
        }
        // Ollama doesn't require API key for local models
        this.providers.set('ollama', new ollama_1.OllamaProvider());
    }
    loadAvailableModels() {
        this.models = [
            // OpenAI Models
            {
                name: 'gpt-4-turbo-preview',
                provider: 'openai',
                maxTokens: 128000,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            {
                name: 'gpt-4',
                provider: 'openai',
                maxTokens: 8192,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            {
                name: 'gpt-3.5-turbo',
                provider: 'openai',
                maxTokens: 16385,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            // Anthropic Models
            {
                name: 'claude-3-opus-20240229',
                provider: 'anthropic',
                maxTokens: 200000,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            {
                name: 'claude-3-sonnet-20240229',
                provider: 'anthropic',
                maxTokens: 200000,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            {
                name: 'claude-3-haiku-20240307',
                provider: 'anthropic',
                maxTokens: 200000,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            // Deepseek Models
            {
                name: 'deepseek-chat',
                provider: 'deepseek',
                maxTokens: 32768,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            {
                name: 'deepseek-coder',
                provider: 'deepseek',
                maxTokens: 16384,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            // Ollama Models (common local models)
            {
                name: 'llama2',
                provider: 'ollama',
                maxTokens: 4096,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            {
                name: 'codellama',
                provider: 'ollama',
                maxTokens: 4096,
                supportsStreaming: true,
                supportsFunctionCalling: true
            },
            {
                name: 'mistral',
                provider: 'ollama',
                maxTokens: 8192,
                supportsStreaming: true,
                supportsFunctionCalling: true
            }
        ];
    }
    async getAvailableModels() {
        // Filter models based on available providers
        return this.models.filter(model => this.providers.has(model.provider));
    }
    async getModel(modelName) {
        const model = this.models.find(m => m.name === modelName);
        if (!model) {
            throw new Error(`Model not found: ${modelName}`);
        }
        if (!this.providers.has(model.provider)) {
            throw new Error(`Provider not available: ${model.provider}`);
        }
        return model;
    }
    async getDefaultModel() {
        return await this.getModel(this.defaultModel);
    }
    async getDefaultConfig() {
        return {
            model: this.defaultModel,
            temperature: 0.7,
            maxTokens: 4096,
            topP: 1,
            frequencyPenalty: 0,
            presencePenalty: 0
        };
    }
    async getConfigForModel(modelName) {
        const model = await this.getModel(modelName);
        return {
            model: modelName,
            temperature: 0.7,
            maxTokens: Math.min(4096, model.maxTokens),
            topP: 1,
            frequencyPenalty: 0,
            presencePenalty: 0
        };
    }
    async generateResponse(messages, context, tools, config) {
        const model = await this.getModel(config.model);
        const provider = this.providers.get(model.provider);
        if (!provider) {
            throw new Error(`Provider not available: ${model.provider}`);
        }
        // Prepare context for the model
        const contextSummary = this.prepareContext(context);
        const systemMessage = this.buildSystemMessage(contextSummary, tools);
        // Add system message to the beginning
        const fullMessages = [systemMessage, ...messages];
        return await provider.generateResponse(fullMessages, tools, config);
    }
    prepareContext(context) {
        if (context.length === 0) {
            return 'No files in context.';
        }
        let contextStr = `Context includes ${context.length} files:\n\n`;
        for (const file of context.slice(0, 50)) { // Limit to 50 files in summary
            contextStr += `File: ${file.path} (${file.language})\n`;
            contextStr += `Size: ${file.size} bytes\n`;
            contextStr += `Content:\n${file.content.slice(0, 2000)}${file.content.length > 2000 ? '...' : ''}\n\n`;
        }
        if (context.length > 50) {
            contextStr += `... and ${context.length - 50} more files\n`;
        }
        return contextStr;
    }
    buildSystemMessage(contextSummary, tools) {
        const toolDescriptions = tools.map(tool => `${tool.name}: ${tool.description}`).join('\n');
        return {
            role: 'system',
            content: `You are Kritrima AI, an advanced coding assistant with autonomous capabilities.

CONTEXT:
${contextSummary}

AVAILABLE TOOLS:
${toolDescriptions}

CAPABILITIES:
- Analyze and understand large codebases
- Execute shell commands autonomously
- Read, write, and edit files
- Search through code and documentation
- Detect and fix errors automatically
- Plan and execute complex development tasks

INSTRUCTIONS:
1. Always analyze the user's request thoroughly
2. Break down complex tasks into smaller steps
3. Use available tools to gather information and execute tasks
4. Provide detailed explanations of your actions
5. Ask for clarification when needed
6. Prioritize code quality and best practices
7. Handle errors gracefully and provide helpful feedback

You can execute tools autonomously to complete tasks efficiently. Always explain what you're doing and why.`
        };
    }
    async streamResponse(messages, context, tools, config, onChunk) {
        const model = await this.getModel(config.model);
        const provider = this.providers.get(model.provider);
        if (!provider) {
            throw new Error(`Provider not available: ${model.provider}`);
        }
        const contextSummary = this.prepareContext(context);
        const systemMessage = this.buildSystemMessage(contextSummary, tools);
        const fullMessages = [systemMessage, ...messages];
        await provider.streamResponse(fullMessages, tools, config, onChunk);
    }
}
exports.ModelManager = ModelManager;
//# sourceMappingURL=manager.js.map