import { Tool, ToolResult } from '../types';
export declare class WebTool implements Tool {
    name: string;
    description: string;
    parameters: {
        type: "object";
        properties: {
            action: {
                type: string;
                enum: string[];
                description: string;
            };
            url: {
                type: string;
                description: string;
            };
            query: {
                type: string;
                description: string;
            };
            headers: {
                type: string;
                description: string;
            };
            data: {
                type: string;
                description: string;
            };
            timeout: {
                type: string;
                description: string;
                default: number;
            };
            follow_redirects: {
                type: string;
                description: string;
                default: boolean;
            };
            save_path: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
    execute(args: {
        action: string;
        url?: string;
        query?: string;
        headers?: Record<string, string>;
        data?: any;
        timeout?: number;
        follow_redirects?: boolean;
        save_path?: string;
    }): Promise<ToolResult>;
    private fetchContent;
    private downloadFile;
    private searchWeb;
    private makeRequest;
    checkUrl(url: string): Promise<ToolResult>;
    extractLinks(html: string): Promise<string[]>;
    private isValidUrl;
}
//# sourceMappingURL=web.d.ts.map