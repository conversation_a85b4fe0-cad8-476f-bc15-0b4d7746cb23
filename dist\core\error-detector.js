"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorDetector = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const events_1 = require("events");
const glob_1 = require("glob");
class ErrorDetector extends events_1.EventEmitter {
    workingDirectory;
    watchers = new Map();
    constructor(workingDirectory) {
        super();
        this.workingDirectory = workingDirectory;
    }
    async scanDirectory(dirPath) {
        const errors = [];
        try {
            const files = await this.getFilesToScan(dirPath);
            for (const file of files) {
                const fileErrors = await this.scanFile(file);
                errors.push(...fileErrors);
            }
            return errors;
        }
        catch (error) {
            throw new Error(`Failed to scan directory: ${error}`);
        }
    }
    async scanFile(filePath) {
        const errors = [];
        try {
            if (!fs.existsSync(filePath)) {
                return errors;
            }
            const content = fs.readFileSync(filePath, 'utf8');
            const language = this.detectLanguage(filePath);
            // Syntax errors
            const syntaxErrors = await this.checkSyntax(filePath, content, language);
            errors.push(...syntaxErrors);
            // Common code issues
            const codeIssues = await this.checkCodeIssues(filePath, content, language);
            errors.push(...codeIssues);
            // Security issues
            const securityIssues = await this.checkSecurity(filePath, content, language);
            errors.push(...securityIssues);
            return errors;
        }
        catch (error) {
            return [{
                    file: filePath,
                    line: 1,
                    column: 1,
                    severity: 'error',
                    message: `Failed to scan file: ${error}`,
                    fixable: false
                }];
        }
    }
    async getFilesToScan(dirPath) {
        const patterns = [
            '**/*.ts', '**/*.js', '**/*.tsx', '**/*.jsx',
            '**/*.py', '**/*.java', '**/*.cpp', '**/*.c',
            '**/*.cs', '**/*.go', '**/*.rs', '**/*.php',
            '**/*.rb', '**/*.swift', '**/*.kt'
        ];
        const ignorePatterns = [
            '**/node_modules/**',
            '**/dist/**',
            '**/build/**',
            '**/.git/**',
            '**/coverage/**'
        ];
        const files = [];
        for (const pattern of patterns) {
            const matches = await (0, glob_1.glob)(pattern, {
                cwd: dirPath,
                ignore: ignorePatterns,
                absolute: true
            });
            files.push(...matches);
        }
        return files;
    }
    async checkSyntax(filePath, content, language) {
        const errors = [];
        switch (language) {
            case 'javascript':
            case 'typescript':
                errors.push(...this.checkJavaScriptSyntax(filePath, content));
                break;
            case 'python':
                errors.push(...this.checkPythonSyntax(filePath, content));
                break;
            case 'json':
                errors.push(...this.checkJSONSyntax(filePath, content));
                break;
        }
        return errors;
    }
    checkJavaScriptSyntax(filePath, content) {
        const errors = [];
        const lines = content.split('\n');
        // Check for common syntax issues
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineNum = i + 1;
            // Unclosed brackets
            const openBrackets = (line.match(/[{[(]/g) || []).length;
            const closeBrackets = (line.match(/[}\])]/g) || []).length;
            // Missing semicolons (simplified check)
            if (line.trim().match(/^(let|const|var|return|throw)\s+.*[^;{}\s]$/)) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: line.length,
                    severity: 'warning',
                    message: 'Missing semicolon',
                    rule: 'missing-semicolon',
                    fixable: true
                });
            }
            // Undefined variables (basic check)
            if (line.includes('undefined') && !line.includes('typeof')) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('undefined') + 1,
                    severity: 'warning',
                    message: 'Possible undefined variable',
                    rule: 'undefined-variable',
                    fixable: false
                });
            }
        }
        return errors;
    }
    checkPythonSyntax(filePath, content) {
        const errors = [];
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineNum = i + 1;
            // Indentation issues
            if (line.match(/^\s*\t\s*/) || line.match(/^\s* \s*\t/)) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: 1,
                    severity: 'error',
                    message: 'Mixed tabs and spaces in indentation',
                    rule: 'indentation',
                    fixable: true
                });
            }
            // Missing colons
            if (line.trim().match(/^(if|elif|else|for|while|def|class|try|except|finally|with)\s+.*[^:]$/)) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: line.length,
                    severity: 'error',
                    message: 'Missing colon',
                    rule: 'missing-colon',
                    fixable: true
                });
            }
        }
        return errors;
    }
    checkJSONSyntax(filePath, content) {
        const errors = [];
        try {
            JSON.parse(content);
        }
        catch (error) {
            const match = error.message.match(/at position (\d+)/);
            const position = match ? parseInt(match[1]) : 0;
            const lines = content.substring(0, position).split('\n');
            errors.push({
                file: filePath,
                line: lines.length,
                column: lines[lines.length - 1].length + 1,
                severity: 'error',
                message: `JSON syntax error: ${error.message}`,
                rule: 'json-syntax',
                fixable: false
            });
        }
        return errors;
    }
    async checkCodeIssues(filePath, content, language) {
        const errors = [];
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineNum = i + 1;
            // TODO comments
            if (line.includes('TODO') || line.includes('FIXME') || line.includes('HACK')) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('TODO') + 1 || line.indexOf('FIXME') + 1 || line.indexOf('HACK') + 1,
                    severity: 'info',
                    message: 'TODO/FIXME comment found',
                    rule: 'todo-comment',
                    fixable: false
                });
            }
            // Console.log statements (for JavaScript/TypeScript)
            if ((language === 'javascript' || language === 'typescript') && line.includes('console.log')) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('console.log') + 1,
                    severity: 'warning',
                    message: 'Console.log statement found',
                    rule: 'no-console',
                    fixable: true
                });
            }
            // Long lines
            if (line.length > 120) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: 121,
                    severity: 'warning',
                    message: 'Line too long (>120 characters)',
                    rule: 'max-line-length',
                    fixable: false
                });
            }
            // Trailing whitespace
            if (line.match(/\s+$/)) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: line.length,
                    severity: 'warning',
                    message: 'Trailing whitespace',
                    rule: 'no-trailing-spaces',
                    fixable: true
                });
            }
        }
        return errors;
    }
    async checkSecurity(filePath, content, language) {
        const errors = [];
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineNum = i + 1;
            // Hardcoded passwords/secrets
            if (line.match(/(password|secret|key|token)\s*[:=]\s*["'][^"']+["']/i)) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: 1,
                    severity: 'error',
                    message: 'Possible hardcoded secret',
                    rule: 'no-hardcoded-secrets',
                    fixable: false
                });
            }
            // SQL injection risks
            if (line.includes('SELECT') && line.includes('+')) {
                errors.push({
                    file: filePath,
                    line: lineNum,
                    column: line.indexOf('SELECT') + 1,
                    severity: 'warning',
                    message: 'Possible SQL injection risk',
                    rule: 'sql-injection',
                    fixable: false
                });
            }
        }
        return errors;
    }
    async fixError(error) {
        if (!error.fixable) {
            throw new Error('Error is not automatically fixable');
        }
        const content = fs.readFileSync(error.file, 'utf8');
        const lines = content.split('\n');
        switch (error.rule) {
            case 'missing-semicolon':
                lines[error.line - 1] += ';';
                break;
            case 'missing-colon':
                lines[error.line - 1] += ':';
                break;
            case 'no-trailing-spaces':
                lines[error.line - 1] = lines[error.line - 1].trimEnd();
                break;
            case 'no-console':
                lines[error.line - 1] = lines[error.line - 1].replace(/console\.log\([^)]*\);?/, '');
                break;
            case 'indentation':
                // Fix mixed tabs and spaces
                lines[error.line - 1] = lines[error.line - 1].replace(/^\s+/, (match) => {
                    return '  '.repeat(match.replace(/\t/g, '  ').length / 2);
                });
                break;
        }
        const newContent = lines.join('\n');
        fs.writeFileSync(error.file, newContent, 'utf8');
        this.emit('errorFixed', error);
    }
    detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.json': 'json'
        };
        return languageMap[ext] || 'text';
    }
    startWatching(dirPath) {
        if (this.watchers.has(dirPath)) {
            return;
        }
        const watcher = fs.watch(dirPath, { recursive: true }, async (eventType, filename) => {
            if (filename && eventType === 'change') {
                const fullPath = path.join(dirPath, filename);
                const errors = await this.scanFile(fullPath);
                for (const error of errors) {
                    this.emit('errorDetected', error);
                }
            }
        });
        this.watchers.set(dirPath, watcher);
    }
    stopWatching(dirPath) {
        const watcher = this.watchers.get(dirPath);
        if (watcher) {
            watcher.close();
            this.watchers.delete(dirPath);
        }
    }
    stopAllWatching() {
        for (const [dirPath, watcher] of this.watchers) {
            watcher.close();
        }
        this.watchers.clear();
    }
}
exports.ErrorDetector = ErrorDetector;
//# sourceMappingURL=error-detector.js.map