import { DiffChange, ReviewSandbox } from '../types';
export declare class DiffReviewer {
    private sandboxes;
    private sandboxDir;
    constructor(sandboxDir?: string);
    private ensureSandboxDir;
    createSandbox(changes: DiffChange[]): ReviewSandbox;
    private createSandboxFiles;
    private createAddDiff;
    private createModifyDiff;
    private createRemoveDiff;
    reviewChanges(sandboxId: string): {
        sandbox: ReviewSandbox;
        analysis: ChangeAnalysis;
    };
    private analyzeChanges;
    private checkForIssues;
    private calculateRiskLevel;
    private generateRecommendations;
    approveSandbox(sandboxId: string): void;
    rejectSandbox(sandboxId: string): void;
    applyChanges(sandboxId: string): void;
    private applyChange;
    private createFile;
    private modifyFile;
    private removeFile;
    getSandbox(sandboxId: string): ReviewSandbox | undefined;
    listSandboxes(): ReviewSandbox[];
    deleteSandbox(sandboxId: string): void;
    private generateSandboxId;
}
interface ChangeAnalysis {
    totalChanges: number;
    addedFiles: number;
    modifiedFiles: number;
    removedFiles: number;
    linesAdded: number;
    linesRemoved: number;
    riskLevel: 'low' | 'medium' | 'high';
    issues: Issue[];
    recommendations: string[];
}
interface Issue {
    type: 'security' | 'code-quality' | 'maintenance' | 'complexity';
    severity: 'low' | 'medium' | 'high';
    message: string;
    line?: number;
}
export {};
//# sourceMappingURL=diff-reviewer.d.ts.map