import { EventEmitter } from 'events';
import { ErrorDetection } from '../types';
export declare class ErrorDetector extends EventEmitter {
    private workingDirectory;
    private watchers;
    constructor(workingDirectory: string);
    scanDirectory(dirPath: string): Promise<ErrorDetection[]>;
    scanFile(filePath: string): Promise<ErrorDetection[]>;
    private getFilesToScan;
    private checkSyntax;
    private checkJavaScriptSyntax;
    private checkPythonSyntax;
    private checkJSONSyntax;
    private checkCodeIssues;
    private checkSecurity;
    fixError(error: ErrorDetection): Promise<void>;
    private detectLanguage;
    startWatching(dirPath: string): void;
    stopWatching(dirPath: string): void;
    stopAllWatching(): void;
}
//# sourceMappingURL=error-detector.d.ts.map