import { Tool, ToolResult } from '../types';
export declare class WriteTool implements Tool {
    name: string;
    description: string;
    parameters: {
        type: "object";
        properties: {
            path: {
                type: string;
                description: string;
            };
            content: {
                type: string;
                description: string;
            };
            encoding: {
                type: string;
                description: string;
                default: string;
            };
            mode: {
                type: string;
                enum: string[];
                description: string;
                default: string;
            };
            create_dirs: {
                type: string;
                description: string;
                default: boolean;
            };
            backup: {
                type: string;
                description: string;
                default: boolean;
            };
        };
        required: string[];
    };
    execute(args: {
        path: string;
        content: string;
        encoding?: string;
        mode?: string;
        create_dirs?: boolean;
        backup?: boolean;
    }): Promise<ToolResult>;
    private appendToFile;
    private createBackup;
    writeMultipleFiles(files: Array<{
        path: string;
        content: string;
        encoding?: string;
    }>): Promise<ToolResult>;
    writeFromTemplate(templatePath: string, outputPath: string, variables: Record<string, string>): Promise<ToolResult>;
    writeJSON(filePath: string, data: any, pretty?: boolean): Promise<ToolResult>;
    writeCSV(filePath: string, data: any[][], headers?: string[]): Promise<ToolResult>;
}
//# sourceMappingURL=write.d.ts.map