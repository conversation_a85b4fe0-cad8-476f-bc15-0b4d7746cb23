"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShellTool = void 0;
const child_process_1 = require("child_process");
const util_1 = require("util");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class ShellTool {
    name = 'shell';
    description = 'Execute shell commands in the local environment. Can run any bash/cmd command and return the output.';
    parameters = {
        type: 'object',
        properties: {
            command: {
                type: 'string',
                description: 'The shell command to execute'
            },
            cwd: {
                type: 'string',
                description: 'Working directory for the command (optional)'
            },
            timeout: {
                type: 'number',
                description: 'Timeout in milliseconds (default: 30000)'
            },
            env: {
                type: 'object',
                description: 'Environment variables to set (optional)'
            }
        },
        required: ['command']
    };
    async execute(args) {
        try {
            const { command, cwd, timeout = 30000, env } = args;
            // Security check - prevent dangerous commands
            if (this.isDangerousCommand(command)) {
                return {
                    success: false,
                    output: '',
                    error: 'Command blocked for security reasons'
                };
            }
            const options = {
                timeout,
                maxBuffer: 1024 * 1024 * 10, // 10MB buffer
                encoding: 'utf8'
            };
            if (cwd) {
                options.cwd = cwd;
            }
            if (env) {
                options.env = { ...process.env, ...env };
            }
            const { stdout, stderr } = await execAsync(command, options);
            return {
                success: true,
                output: String(stdout || stderr || 'Command executed successfully'),
                metadata: {
                    command,
                    cwd: cwd || process.cwd(),
                    exitCode: 0
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: error.stdout || '',
                error: error.stderr || error.message || 'Command execution failed',
                metadata: {
                    command: args.command,
                    cwd: args.cwd || process.cwd(),
                    exitCode: error.code || 1
                }
            };
        }
    }
    async executeInteractive(args) {
        return new Promise((resolve) => {
            const { command, cwd, env, onOutput, onError } = args;
            if (this.isDangerousCommand(command)) {
                resolve({
                    success: false,
                    output: '',
                    error: 'Command blocked for security reasons'
                });
                return;
            }
            const [cmd, ...cmdArgs] = command.split(' ');
            const options = {};
            if (cwd) {
                options.cwd = cwd;
            }
            if (env) {
                options.env = { ...process.env, ...env };
            }
            const child = (0, child_process_1.spawn)(cmd, cmdArgs, options);
            let stdout = '';
            let stderr = '';
            child.stdout?.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                onOutput?.(output);
            });
            child.stderr?.on('data', (data) => {
                const error = data.toString();
                stderr += error;
                onError?.(error);
            });
            child.on('close', (code) => {
                resolve({
                    success: code === 0,
                    output: stdout,
                    error: stderr,
                    metadata: {
                        command,
                        cwd: cwd || process.cwd(),
                        exitCode: code || 0
                    }
                });
            });
            child.on('error', (error) => {
                resolve({
                    success: false,
                    output: stdout,
                    error: error.message,
                    metadata: {
                        command,
                        cwd: cwd || process.cwd(),
                        exitCode: 1
                    }
                });
            });
        });
    }
    isDangerousCommand(command) {
        const dangerousPatterns = [
            /rm\s+-rf\s+\//, // rm -rf /
            /rm\s+-rf\s+\*/, // rm -rf *
            /format\s+c:/, // format c:
            /del\s+\/s\s+\/q\s+c:\\/, // del /s /q c:\
            /shutdown/, // shutdown commands
            /reboot/, // reboot commands
            /halt/, // halt commands
            /mkfs/, // filesystem formatting
            /dd\s+if=.*of=\/dev/, // disk writing
            /:(){ :|:& };:/, // fork bomb
            /curl.*\|\s*sh/, // curl | sh
            /wget.*\|\s*sh/, // wget | sh
            /chmod\s+777\s+\//, // chmod 777 /
            /chown.*\//, // chown on root
            /sudo\s+rm/, // sudo rm
            /sudo\s+dd/, // sudo dd
        ];
        return dangerousPatterns.some(pattern => pattern.test(command.toLowerCase()));
    }
    async getSystemInfo() {
        const commands = process.platform === 'win32'
            ? ['systeminfo', 'wmic os get caption,version']
            : ['uname -a', 'lsb_release -a || cat /etc/os-release'];
        let output = '';
        for (const cmd of commands) {
            try {
                const result = await this.execute({ command: cmd });
                if (result.success) {
                    output += `${cmd}:\n${result.output}\n\n`;
                }
            }
            catch (error) {
                // Continue with next command
            }
        }
        return {
            success: true,
            output: output || 'System information not available',
            metadata: {
                platform: process.platform,
                arch: process.arch,
                nodeVersion: process.version
            }
        };
    }
    async listProcesses() {
        const command = process.platform === 'win32' ? 'tasklist' : 'ps aux';
        return await this.execute({ command });
    }
    async getCurrentDirectory() {
        const command = process.platform === 'win32' ? 'cd' : 'pwd';
        return await this.execute({ command });
    }
    async listFiles(directory) {
        const dir = directory || '.';
        const command = process.platform === 'win32'
            ? `dir "${dir}"`
            : `ls -la "${dir}"`;
        return await this.execute({ command, cwd: directory });
    }
}
exports.ShellTool = ShellTool;
//# sourceMappingURL=shell.js.map