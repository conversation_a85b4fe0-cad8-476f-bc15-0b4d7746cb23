import { Message, Tool, ModelConfig, StreamingResponse } from '../../types';
export declare class OpenAIProvider {
    private client;
    constructor(apiKey: string);
    generateResponse(messages: Message[], tools: Tool[], config: ModelConfig): Promise<StreamingResponse>;
    streamResponse(messages: Message[], tools: Tool[], config: ModelConfig, onChunk: (chunk: string) => void): Promise<void>;
    private convertMessages;
    private convertTools;
    testConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
}
//# sourceMappingURL=openai.d.ts.map