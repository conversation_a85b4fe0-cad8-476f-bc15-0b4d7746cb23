{"version": 3, "file": "diff-reviewer.js", "sourceRoot": "", "sources": ["../../src/core/diff-reviewer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,+BAAmE;AAGnE,MAAa,YAAY;IACf,SAAS,GAA+B,IAAI,GAAG,EAAE,CAAC;IAClD,UAAU,CAAS;IAE3B,YAAY,aAAqB,mBAAmB;QAClD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACpC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,aAAa,CAAC,OAAqB;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAkB;YAC7B,EAAE,EAAE,SAAS;YACb,OAAO;YACP,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAEjC,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,kBAAkB,CAAC,OAAsB;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QAE3D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAChC,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,oCAAoC;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YAE5D,IAAI,IAAY,CAAC;YAEjB,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,KAAK;oBACR,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;oBAClC,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;oBACrC,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;oBACrC,MAAM;gBACR;oBACE,SAAS;YACb,CAAC;YAED,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAC3D,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC;YAC3C,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,YAAY,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;YACpC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACjC,IAAI,EAAE,CAAC,CAAC,IAAI;gBACZ,IAAI,EAAE,CAAC,CAAC,IAAI;gBACZ,UAAU,EAAE,CAAC,CAAC,UAAU;aACzB,CAAC,CAAC;SACJ,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACvB,CAAC;IAEO,aAAa,CAAC,MAAkB;QACtC,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC;QAClC,OAAO,IAAA,0BAAmB,EACxB,WAAW,EACX,MAAM,CAAC,IAAI,EACX,EAAE,EACF,UAAU,EACV,EAAE,EACF,EAAE,CACH,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,MAAkB;QACzC,MAAM,eAAe,GAAG,MAAM,CAAC,eAAe,IAAI,EAAE,CAAC;QACrD,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC;QAElC,OAAO,IAAA,0BAAmB,EACxB,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,IAAI,EACX,eAAe,EACf,UAAU,EACV,EAAE,EACF,EAAE,CACH,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,MAAkB;QACzC,MAAM,eAAe,GAAG,MAAM,CAAC,eAAe,IAAI,EAAE,CAAC;QACrD,OAAO,IAAA,0BAAmB,EACxB,MAAM,CAAC,IAAI,EACX,WAAW,EACX,eAAe,EACf,EAAE,EACF,EAAE,EACF,EAAE,CACH,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,SAAiB;QAI7B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEtD,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAC/B,CAAC;IAEO,cAAc,CAAC,OAAqB;QAC1C,MAAM,QAAQ,GAAmB;YAC/B,YAAY,EAAE,OAAO,CAAC,MAAM;YAC5B,UAAU,EAAE,CAAC;YACb,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,UAAU,EAAE,CAAC;YACb,YAAY,EAAE,CAAC;YACf,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,EAAE;YACV,eAAe,EAAE,EAAE;SACpB,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,KAAK;oBACR,QAAQ,CAAC,UAAU,EAAE,CAAC;oBACtB,QAAQ,CAAC,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;oBACzD,MAAM;gBACR,KAAK,QAAQ;oBACX,QAAQ,CAAC,aAAa,EAAE,CAAC;oBACzB,MAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;oBACnE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;oBACnD,IAAI,QAAQ,GAAG,QAAQ,EAAE,CAAC;wBACxB,QAAQ,CAAC,UAAU,IAAI,QAAQ,GAAG,QAAQ,CAAC;oBAC7C,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,YAAY,IAAI,QAAQ,GAAG,QAAQ,CAAC;oBAC/C,CAAC;oBACD,MAAM;gBACR,KAAK,QAAQ;oBACX,QAAQ,CAAC,YAAY,EAAE,CAAC;oBACxB,QAAQ,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;oBAC3E,MAAM;YACV,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC;QAED,uBAAuB;QACvB,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAEvD,2BAA2B;QAC3B,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAElE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,cAAc,CAAC,MAAkB,EAAE,QAAwB;QACjE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAE/B,sCAAsC;QACtC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1F,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,MAAM;gBAChB,OAAO,EAAE,iCAAiC,MAAM,CAAC,IAAI,EAAE;gBACvD,IAAI,EAAE,MAAM,CAAC,UAAU;aACxB,CAAC,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YACpC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,4BAA4B,MAAM,CAAC,IAAI,EAAE;gBAClD,IAAI,EAAE,MAAM,CAAC,UAAU;aACxB,CAAC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,yBAAyB,MAAM,CAAC,IAAI,EAAE;gBAC/C,IAAI,EAAE,MAAM,CAAC,UAAU;aACxB,CAAC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACrC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,gCAAgC,MAAM,CAAC,IAAI,EAAE;gBACtD,IAAI,EAAE,MAAM,CAAC,UAAU;aACxB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,QAAwB;QACjD,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,kCAAkC;QAClC,SAAS,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;QACrC,SAAS,IAAI,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC;QACxC,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,CAAC,CAAC;QAEvC,kCAAkC;QAClC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;QAE5E,4BAA4B;QAC5B,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpC,QAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACvB,KAAK,MAAM;oBACT,SAAS,IAAI,EAAE,CAAC;oBAChB,MAAM;gBACR,KAAK,QAAQ;oBACX,SAAS,IAAI,CAAC,CAAC;oBACf,MAAM;gBACR,KAAK,KAAK;oBACR,SAAS,IAAI,CAAC,CAAC;oBACf,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAI,SAAS,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QACnC,IAAI,SAAS,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QACrC,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,uBAAuB,CAAC,QAAwB;QACtD,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,EAAE,CAAC;YACrD,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAClE,eAAe,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;YACtD,eAAe,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YACpE,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC;YACzD,eAAe,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YAC/D,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;YAClC,eAAe,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACvD,eAAe,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,cAAc,CAAC,SAAiB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;IAC9B,CAAC;IAED,aAAa,CAAC,SAAiB;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;IAC9B,CAAC;IAED,YAAY,CAAC,SAAiB;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,MAAkB;QACpC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,KAAK;gBACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC7B,MAAM;QACV,CAAC;IACH,CAAC;IAEO,UAAU,CAAC,QAAgB,EAAE,OAAe;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACzC,CAAC;QACD,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IAEO,UAAU,CAAC,QAAgB,EAAE,OAAe;QAClD,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IAEO,UAAU,CAAC,QAAgB;QACjC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,UAAU,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED,aAAa;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,aAAa,CAAC,SAAiB;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC1D,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAEO,iBAAiB;QACvB,OAAO,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC5E,CAAC;CACF;AAzWD,oCAyWC"}